2025-06-27 11:19:47.300 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-27 11:19:47.308 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-27 11:19:47.340 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-27 11:19:47.341 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-27 11:21:03.517 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-27 11:21:03.527 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-27 11:21:03.564 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-27 11:21:03.565 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-27 11:21:09.084 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-27 11:21:09.704 [http-nio-8080-exec-1] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-27 11:21:09.782 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-27 11:21:09.894 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-27 11:21:10.179 [http-nio-8080-exec-1] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_1167697449.1751003470057"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@57dbc26a; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-13286633324316616057.tmp
2025-06-27 11:21:10.429 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-27 11:21:11.744 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 1312 ms
2025-06-27 11:21:11.745 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '8fa3d3de-8db7-4aa1-a310-503803c5eb0f@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-27 11:21:11.750 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-27 11:21:11.754 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x051287cd: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x3fa5dbf5: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-27 11:21:11.759 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-27 11:21:11.776 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-27 11:21:11.780 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-27 11:21:11.783 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-27 11:21:11.784 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-27 11:21:11.785 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-27 11:21:11.806 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-27T05:51:11.735319600Z</eb:Timestamp>
          <eb:MessageId>177d1d7e-76bb-4a6a-bc87-6a30a508fe27</eb:MessageId>
          <eb:RefToMessageId>8fa3d3de-8db7-4aa1-a310-503803c5eb0f@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>U4+zJERcuuDjxzFzthMQviL4PzFAhoRcUZ+34p93w18=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-27 11:21:11.806 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-27 11:21:11.813 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@c28fc3c
2025-06-27 11:21:11.832 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-27 11:21:11.833 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-27 11:22:41.881 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-27 11:22:56.219 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-27 11:22:56.230 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-27 11:22:56.272 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-27 11:22:56.273 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-27 11:23:12.433 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-27 11:23:13.096 [http-nio-8080-exec-1] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-27 11:23:13.191 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-27 11:23:13.298 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-27 11:23:13.486 [http-nio-8080-exec-1] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_592957076.1751003593405"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@2d6e241e; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-10151744053630547002.tmp
2025-06-27 11:23:13.546 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-27 11:23:14.974 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 1417 ms
2025-06-27 11:23:14.975 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '22492dc0-ce39-4d43-9fa7-57c4e00e06a4@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-27 11:23:14.982 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-27 11:23:14.986 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x2495b2ad: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x05060e3f: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-27 11:23:14.991 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-27 11:23:15.017 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-27 11:23:15.020 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-27 11:23:15.023 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-27 11:23:15.025 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-27 11:23:15.025 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-27 11:23:15.071 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-27T05:53:14.956484800Z</eb:Timestamp>
          <eb:MessageId>cc5fece6-0e97-4fdc-8e85-0238c32dfc81</eb:MessageId>
          <eb:RefToMessageId>22492dc0-ce39-4d43-9fa7-57c4e00e06a4@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>R6wc/53f6flMDww+Z7T3AWxSWvXbozC2RmSiqLZx/fs=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-27 11:23:15.071 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-27 11:23:15.080 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@137b83af
2025-06-27 11:23:15.101 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-27 11:23:15.103 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-27 11:23:29.403 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-27 12:31:51.347 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-27 12:31:51.357 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-27 12:31:51.391 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-27 12:31:51.392 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-27 12:31:57.843 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://***************:8080/suntec/as4' with max. 1 retries
2025-06-27 12:31:58.450 [http-nio-8080-exec-2] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-27 12:31:58.534 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-27 12:31:58.631 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-27 12:31:58.817 [http-nio-8080-exec-2] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_97596668.1751007718745"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@5a193fc6; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-5376574999162602488.tmp
2025-06-27 12:31:58.883 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://***************:8080/suntec/as4'
2025-06-27 12:31:59.178 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://***************:8080/suntec/as4' after 291 ms
2025-06-27 12:31:59.178 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '46413538-fe25-4f7e-a66f-26541ac8a7f1@phase4' to 'http://***************:8080/suntec/as4'
2025-06-27 12:31:59.184 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-27 12:31:59.189 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x1bc48faa: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x45acdc3c: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-27 12:31:59.198 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-27 12:31:59.215 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-27 12:31:59.217 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-27 12:31:59.219 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-27 12:31:59.222 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-27 12:31:59.223 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-27 12:31:59.263 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:env="http://www.w3.org/2003/05/soap-envelope"><env:Header><eb:Messaging xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" env:mustUnderstand="true" wsu:Id="_11f67835-19a7-4207-b226-063da5098332"><eb:SignalMessage xmlns:ns3="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader" xmlns:ns4="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns6="http://www.w3.org/2000/09/xmldsig#" xmlns:ns7="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0"><eb:MessageInfo><eb:Timestamp>2025-06-27T12:27:36.391+05:30</eb:Timestamp><eb:MessageId><EMAIL></eb:MessageId><eb:RefToMessageId>46413538-fe25-4f7e-a66f-26541ac8a7f1@phase4</eb:RefToMessageId></eb:MessageInfo><eb:Receipt><ns7:NonRepudiationInformation><ns7:MessagePartNRInformation><ns6:Reference URI="#phase4-msg-0d913986-7f24-4386-b332-c51119b9591e"><ns6:Transforms><ns6:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"><ec:InclusiveNamespaces xmlns:S12="http://www.w3.org/2003/05/soap-envelope" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:ec="http://www.w3.org/2001/10/xml-exc-c14n#" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" PrefixList="S12"/></ns6:Transform></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>+YjlDywjEm+uz64+1rwh9xhOiWlg5BV1+jXMVOMEh+0=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation><ns7:MessagePartNRInformation><ns6:Reference URI="#id-fca5719a-883b-4900-9482-f57eb8c8bbd5"><ns6:Transforms><ns6:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>bfhcsuQx/ewd1bMIEvT090y5caTvMqC+7Pmwc7OicZ8=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation><ns7:MessagePartNRInformation><ns6:Reference URI="cid:phase4-att-811243eb-4db0-42e5-bdcd-aa287f03c7e7@cid"><ns6:Transforms><ns6:Transform Algorithm="http://docs.oasis-open.org/wss/oasis-wss-SwAProfile-1.1#Attachment-Content-Signature-Transform"/></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>r/UvW9sRKq+RjTOfDQgKdbBogVdo1UB0gfFOR2EhOtQ=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation></ns7:NonRepudiationInformation></eb:Receipt></eb:SignalMessage></eb:Messaging><wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" env:mustUnderstand="true"><wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="G39e1c70c-f491-4430-ad91-90a4b287847e">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</wsse:BinarySecurityToken><wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="X509-ecef230d-7320-4816-bbad-d2c1dcdfb8b5">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</wsse:BinarySecurityToken><ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#" Id="SIG-756adf8c-e527-4dd4-9221-a67fc959562e"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"><ec:InclusiveNamespaces xmlns:ec="http://www.w3.org/2001/10/xml-exc-c14n#" PrefixList="env"/></ds:CanonicalizationMethod><ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/><ds:Reference URI="#_66adfc78-fad0-43ff-a4be-deafebd42806"><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>SOApChgAoj+XayZeipin55R7su8ADAq2zYRlzO3yOzU=</ds:DigestValue></ds:Reference><ds:Reference URI="#_11f67835-19a7-4207-b226-063da5098332"><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>5cOViyGjmHghUYnFfR1Rwh7B5qcJPgF6te9SNgI19yI=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue>ZGyD9fS9ZPOZGX5WSnzcOq5L9/kRjw2Lh80vliG9Yf2xbz9aZM14UmcnUTVkBfIG4gf4rxHX3Ct8cOcvk7k0pYJ21KrUQMAkCsKVdBUFCzY8EtPQCsRmadoWlH0CHGwdYlskWl4ei3bFMI3MdpwPODGLHP+OGtM+vfu1jcVpvkZHDveIkNlYm0oOK1cRf5Dyg8oWFC9nF08YdeZ8NLxoywfIVts2c8RjlgbG5uEs+gi1sPvQtlDReqQI3lYRIZJwm4xz0mt8gSK3oMG9mBqMadenMaZYuDEEqbH02cmZaBhu9PD0FoQjprl/DamDWnkASHE0xA4iCGF9LbGysWHFmA==</ds:SignatureValue><ds:KeyInfo Id="KI-4be6575e-5b4d-450d-bb92-bcadbe99cd70"><wsse:SecurityTokenReference wsu:Id="STR-81ed8641-4578-44fc-8ee5-924fbefb4bd9"><wsse:Reference URI="#X509-ecef230d-7320-4816-bbad-d2c1dcdfb8b5" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3"/></wsse:SecurityTokenReference></ds:KeyInfo></ds:Signature></wsse:Security></env:Header><env:Body xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" wsu:Id="_66adfc78-fad0-43ff-a4be-deafebd42806"/></env:Envelope>
2025-06-27 12:31:59.263 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-27 12:31:59.269 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@53db4a8c
2025-06-27 12:31:59.314 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorWSS4J@2322518
2025-06-27 12:31:59.315 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - Using signature algorithm RSA_SHA_256
2025-06-27 12:31:59.316 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - Using signature digest algorithm DIGEST_SHA_256
2025-06-27 12:31:59.351 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {signature-value=[B@4fb3a313, subject=null, canonicalization-method=http://www.w3.org/2001/10/xml-exc-c14n#, public-key=null, x509-certificates=[Ljava.security.cert.X509Certificate;@29f829ec, signature-method=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256, secret=null, principal=CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, data-ref-uris=[org.apache.wss4j.dom.WSDataRef@9c0d8a0, org.apache.wss4j.dom.WSDataRef@69ce0378], x509-reference-type=DIRECT_REF, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=true, action=2, id=SIG-756adf8c-e527-4dd4-9221-a67fc959562e, token-element=[ds:Signature: null]}
2025-06-27 12:31:59.361 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {binary-security-token=<wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="X509-ecef230d-7320-4816-bbad-d2c1dcdfb8b5">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</wsse:BinarySecurityToken>, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=false, action=4096, x509-certificates=[Ljava.security.cert.X509Certificate;@29f829ec, id=X509-ecef230d-7320-4816-bbad-d2c1dcdfb8b5, token-element=[wsse:BinarySecurityToken: null]}
2025-06-27 12:31:59.364 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {binary-security-token=<wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="G39e1c70c-f491-4430-ad91-90a4b287847e">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</wsse:BinarySecurityToken>, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=false, action=4096, x509-certificates=[Ljava.security.cert.X509Certificate;@3c3113b3, id=G39e1c70c-f491-4430-ad91-90a4b287847e, token-element=[wsse:BinarySecurityToken: null]}
2025-06-27 12:31:59.366 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-27 12:32:32.646 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
