2025-06-26 12:41:13.861 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 12:41:13.907 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 12:41:13.965 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 12:41:13.966 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 12:41:26.911 [http-nio-8080-exec-3] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-26 12:41:27.604 [http-nio-8080-exec-3] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-26 12:41:27.715 [http-nio-8080-exec-3] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-26 12:41:27.829 [http-nio-8080-exec-3] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-26 12:41:28.014 [http-nio-8080-exec-3] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_554630611.1750921887943"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@5a0543ca; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-15961745489181586551.tmp
2025-06-26 12:41:28.077 [http-nio-8080-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 12:42:08.767 [http-nio-8080-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 40686 ms
2025-06-26 12:42:08.768 [http-nio-8080-exec-3] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '27360108-58e8-4e02-9417-1be2b554fd9b@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 12:42:08.778 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-26 12:42:08.786 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x6e0d8161: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x058c164b: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-26 12:42:08.793 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-26 12:42:08.818 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-26 12:42:08.822 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-26 12:42:08.827 [http-nio-8080-exec-3] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-26 12:42:08.829 [http-nio-8080-exec-3] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 12:42:08.829 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-26 12:42:08.859 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T07:12:07.949683600Z</eb:Timestamp>
          <eb:MessageId>5caf8a70-ee1e-418d-964f-bd6a40c976cd</eb:MessageId>
          <eb:RefToMessageId>27360108-58e8-4e02-9417-1be2b554fd9b@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>GuvwwH8xmTsJ2M8HLZcfc9ytZ6ivjJw9rJeGvYuAp7c=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-26 12:42:08.859 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-26 12:42:08.870 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@583b3524
2025-06-26 12:42:08.890 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 12:42:08.891 [http-nio-8080-exec-3] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-26 12:42:17.515 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-26 12:50:31.552 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 12:50:31.566 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 12:50:31.605 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 12:50:31.606 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 15:43:56.452 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 15:43:56.470 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 15:43:56.516 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 15:43:56.517 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 15:44:21.614 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 15:44:21.630 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 15:44:21.688 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 15:44:21.690 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 15:44:52.754 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-26 15:44:53.484 [http-nio-8080-exec-2] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-26 15:44:53.569 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-26 15:44:53.674 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-26 15:44:53.876 [http-nio-8080-exec-2] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_222181542.1750932893791"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@43e009cb; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-1179311415838099545.tmp
2025-06-26 15:44:53.948 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 15:45:02.412 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 8461 ms
2025-06-26 15:45:02.413 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID 'fa157aaa-4b72-4611-8a1a-62abfd572f52@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 15:45:02.420 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-26 15:45:02.425 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x7b896fa8: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x7f4f1b66: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-26 15:45:02.439 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-26 15:45:02.457 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-26 15:45:02.459 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-26 15:45:02.463 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-26 15:45:02.465 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 15:45:02.466 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-26 15:45:02.507 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T10:14:59.134598400Z</eb:Timestamp>
          <eb:MessageId>f5027282-ee3f-415b-b030-aec5ec6b5820</eb:MessageId>
          <eb:RefToMessageId>fa157aaa-4b72-4611-8a1a-62abfd572f52@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>pmPzP89tdkw4bOFpGZOHMGHaKi4bUdbFzfTJI/tfKK0=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-26 15:45:02.507 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-26 15:45:02.517 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@4f8995dd
2025-06-26 15:45:02.536 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 15:45:02.539 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-26 15:45:07.318 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-26 17:09:06.910 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 17:09:06.920 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 17:09:06.960 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 17:09:06.960 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 17:09:24.731 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-26 17:09:25.393 [http-nio-8080-exec-2] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-26 17:09:25.480 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-26 17:09:25.594 [http-nio-8080-exec-2] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-26 17:09:25.775 [http-nio-8080-exec-2] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_188622787.1750937965707"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@4679b340; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-2934823155883231153.tmp
2025-06-26 17:09:25.836 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:09:32.594 [http-nio-8080-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 6753 ms
2025-06-26 17:09:32.596 [http-nio-8080-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID 'dea5b313-adba-4b43-8925-e5794de1f529@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:09:32.604 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-26 17:09:32.609 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x7406358c: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x0d2ca382: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-26 17:09:32.615 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-26 17:09:32.632 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-26 17:09:32.635 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-26 17:09:32.638 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-26 17:09:32.640 [http-nio-8080-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 17:09:32.640 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-26 17:09:32.663 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T11:39:29.024512200Z</eb:Timestamp>
          <eb:MessageId>76ec8d17-8715-4189-9447-57bbd2cde97c</eb:MessageId>
          <eb:RefToMessageId>dea5b313-adba-4b43-8925-e5794de1f529@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>UT4BbNqlS3WcmgansV6Fs21PiYF9ll6Uh61hd6iV23s=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-26 17:09:32.663 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-26 17:09:32.671 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@7a2df94e
2025-06-26 17:09:32.688 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 17:09:32.692 [http-nio-8080-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-26 17:15:03.462 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-26 17:47:56.452 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 17:47:56.474 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 17:47:56.547 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 17:47:56.549 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 17:51:04.234 [http-nio-8080-exec-6] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-26 17:51:10.465 [http-nio-8080-exec-6] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-26 17:51:11.086 [http-nio-8080-exec-6] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-26 17:51:13.131 [http-nio-8080-exec-6] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-26 17:51:17.013 [http-nio-8080-exec-6] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_2035309812.1750940476612"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@30c7c5a1; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-14526655878542189212.tmp
2025-06-26 17:51:19.877 [http-nio-8080-exec-6] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:51:24.908 [http-nio-8080-exec-6] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 5001 ms
2025-06-26 17:51:25.060 [http-nio-8080-exec-6] WARN  c.helger.phase4.http.BasicHttpPoster - Error sending message 'f6fe3027-0cfa-4832-8120-21021893016e@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message': ExtendedHttpResponseException -  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=766
  Date=Thu, 26 Jun 2025 12:21:24 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T12:21:24.805318900Z</eb:Timestamp>
          <eb:MessageId>89db5767-2825-454c-89c6-0eaa11808537</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">❌ AES-GCM decryption failed: Tag mismatch</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope> - waiting 10000 ms, than retrying
2025-06-26 17:51:35.104 [http-nio-8080-exec-6] INFO  c.helger.phase4.http.BasicHttpPoster - Retry #1/1 for sending message with ID 'f6fe3027-0cfa-4832-8120-21021893016e@phase4'
2025-06-26 17:51:35.105 [http-nio-8080-exec-6] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:51:35.171 [http-nio-8080-exec-6] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 66 ms
2025-06-26 17:51:35.220 [http-nio-8080-exec-6] ERROR c.h.p.s.AbstractAS4UserMessageBuilder - Exception sending AS4 user message
com.helger.phase4.util.Phase4Exception: Wrapped Phase4Exception
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:237)
	at com.helger.phase4.sender.AbstractAS4MessageBuilder.sendMessage(AbstractAS4MessageBuilder.java:856)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:798)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:764)
	at com.morohub.apsp.core.service.AS4ConversionService.sendAS4Message(AS4ConversionService.java:819)
	at com.morohub.apsp.core.service.AS4ConversionService.convertAndSend(AS4ConversionService.java:219)
	at com.morohub.apsp.api.controller.XmlValidationController.validateAndConvertToAs4(XmlValidationController.java:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.helger.httpclient.response.ExtendedHttpResponseException:  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=766
  Date=Thu, 26 Jun 2025 12:21:35 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T12:21:35.167169800Z</eb:Timestamp>
          <eb:MessageId>d02ce80d-c5bd-4b70-a6f2-0f5d437342c3</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">❌ AES-GCM decryption failed: Tag mismatch</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
	at com.helger.httpclient.response.ExtendedHttpResponseException.create(ExtendedHttpResponseException.java:211)
	at com.helger.httpclient.response.ResponseHandlerHttpEntity.handleResponse(ResponseHandlerHttpEntity.java:55)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.lambda$sendAS4UserMessageAndReceiveAS4SignalMessage$0(AS4BidirectionalClientHelper.java:125)
	at com.helger.phase4.client.AbstractAS4Client.lambda$sendMessageWithRetries$1(AbstractAS4Client.java:589)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:247)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:102)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessage(BasicHttpPoster.java:201)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessageWithRetries(BasicHttpPoster.java:317)
	at com.helger.phase4.client.AbstractAS4Client.sendMessageWithRetries(AbstractAS4Client.java:591)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.sendAS4UserMessageAndReceiveAS4SignalMessage(AS4BidirectionalClientHelper.java:137)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:211)
	... 54 common frames omitted
2025-06-26 17:53:46.220 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-06-26 17:54:06.325 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-06-26 17:54:06.339 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-06-26 17:54:06.378 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-06-26 17:54:06.381 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-06-26 17:54:25.382 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8080/reverse-flow/receive-as4-message' with max. 1 retries
2025-06-26 17:54:26.227 [http-nio-8080-exec-1] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-06-26 17:54:26.307 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-06-26 17:54:26.421 [http-nio-8080-exec-1] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-06-26 17:54:26.653 [http-nio-8080-exec-1] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_546143733.1750940666573"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@4822631c; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-16245410056545616488.tmp
2025-06-26 17:54:26.803 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:54:30.226 [http-nio-8080-exec-1] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8080/reverse-flow/receive-as4-message' after 3419 ms
2025-06-26 17:54:30.227 [http-nio-8080-exec-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '9eb22165-683a-4541-9cbf-7db16b0c9845@phase4' to 'http://localhost:8080/reverse-flow/receive-as4-message'
2025-06-26 17:54:30.234 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-06-26 17:54:30.238 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x39dbf23e: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x71d74ccf: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-06-26 17:54:30.244 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-06-26 17:54:30.363 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-06-26 17:54:30.368 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-06-26 17:54:30.371 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-06-26 17:54:30.374 [http-nio-8080-exec-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 17:54:30.374 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-06-26 17:54:30.400 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-06-26T12:24:28.564088Z</eb:Timestamp>
          <eb:MessageId>e1c9ae69-e43c-4fe9-8b0b-f4dd3b22b2ef</eb:MessageId>
          <eb:RefToMessageId>9eb22165-683a-4541-9cbf-7db16b0c9845@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>q8u3aLbvDYBY++md3L7zDp3S5aYlf0Q87+I3kaqRArM=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-06-26 17:54:30.401 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-06-26 17:54:30.409 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@354e6ef7
2025-06-26 17:54:30.431 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-06-26 17:54:30.433 [http-nio-8080-exec-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-06-26 17:54:37.616 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
