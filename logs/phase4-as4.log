2025-06-27 11:19:39.363 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 16492 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-27 11:19:39.366 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-27 11:19:39.367 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-27 11:19:41.918 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-27 11:19:41.919 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-27 11:19:46.842 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration...
2025-06-27 11:19:46.880 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-06-27 11:19:46.881 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-27 11:19:46.968 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-06-27 11:19:46.969 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-06-27 11:19:46.970 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:19:46.970 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:19:46.970 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:19:46.971 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-06-27 11:19:46.971 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:19:46.971 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:19:46.971 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:19:46.971 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-06-27 11:19:46.972 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:19:46.972 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:19:46.972 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:19:46.972 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-06-27 11:19:46.972 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:19:46.972 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:19:46.973 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:19:46.973 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-06-27 11:19:46.973 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:19:46.973 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:19:46.973 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:19:46.974 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-27 11:19:46.974 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-06-27 11:19:46.974 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:19:46.974 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:19:47.038 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-27 11:19:47.078 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-27 11:19:47.084 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-27 11:19:47.118 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-27 11:19:47.118 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-27 11:19:47.119 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 11:19:47.119 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:19:47.120 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-27 11:19:47.120 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-27 11:19:47.120 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-27 11:19:47.121 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-27 11:19:47.128 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-27 11:19:47.147 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-27 11:19:47.148 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-27 11:19:47.148 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-27 11:19:47.192 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-27 11:19:47.193 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-27 11:19:47.199 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-27 11:19:47.200 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:19:47.341 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-27 11:19:47.341 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-27 11:19:47.409 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 11:19:47.907 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-27 11:19:47.907 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-27 11:19:47.919 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.495 seconds (process running for 10.219)
2025-06-27 11:20:47.777 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-27 11:20:47.780 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-27 11:20:55.316 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 15240 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-27 11:20:55.318 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-27 11:20:55.319 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-27 11:20:58.656 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-27 11:20:58.657 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-27 11:21:03.066 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration...
2025-06-27 11:21:03.093 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-06-27 11:21:03.095 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-27 11:21:03.186 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-06-27 11:21:03.187 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-06-27 11:21:03.188 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:21:03.188 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:21:03.188 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:21:03.188 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-06-27 11:21:03.189 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:21:03.189 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:21:03.190 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:21:03.190 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-06-27 11:21:03.190 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:21:03.191 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:21:03.191 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:21:03.191 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-06-27 11:21:03.191 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:21:03.191 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:21:03.191 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:21:03.192 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-06-27 11:21:03.192 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:21:03.192 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:21:03.192 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:21:03.192 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-27 11:21:03.192 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-06-27 11:21:03.193 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:21:03.193 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:21:03.254 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-27 11:21:03.292 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-27 11:21:03.300 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-27 11:21:03.334 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-27 11:21:03.335 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-27 11:21:03.335 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 11:21:03.335 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:21:03.336 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-27 11:21:03.336 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-27 11:21:03.337 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-27 11:21:03.337 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-27 11:21:03.345 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-27 11:21:03.345 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-27 11:21:03.345 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-27 11:21:03.345 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-27 11:21:03.345 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:21:03.346 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-27 11:21:03.346 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-27 11:21:03.355 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-27 11:21:03.356 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-27 11:21:03.356 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-27 11:21:03.403 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-27 11:21:03.403 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-27 11:21:03.408 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-27 11:21:03.411 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:21:03.565 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-27 11:21:03.566 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-27 11:21:03.627 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 11:21:04.122 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-27 11:21:04.123 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-27 11:21:04.139 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.785 seconds (process running for 10.505)
2025-06-27 11:21:06.854 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-06-27 11:21:06.854 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-06-27 11:21:06.855 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-06-27 11:21:07.591 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 11:21:07.591 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-06-27 11:21:07.591 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-06-27 11:21:07.616 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 11:21:07.616 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 0
2025-06-27 11:21:07.617 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-27 11:21:07.617 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 44f69fb9-6aff-4feb-a232-72f33020e725
2025-06-27 11:21:07.617 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-27 11:21:07.617 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-27 11:21:07.635 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ Unknown document type: Invoice, defaulting to INVOICE
2025-06-27 11:21:07.640 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-27 11:21:07.640 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-27 11:21:07.641 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-27 11:21:07.642 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-27 11:21:07.670 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-27 11:21:07.671 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-27 11:21:07.671 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-27 11:21:07.671 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-27 11:21:07.675 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-27 11:21:07.677 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-27 11:21:07.677 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-27 11:21:07.680 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-27 11:21:07.681 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-27 11:21:07.681 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1751003467678
2025-06-27 11:21:07.681 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x1521471d: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:21:07.683 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x019f27e5: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:21:07.684 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x0a4277b3: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-27 11:21:07.684 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x0aeebc6f: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-27 11:21:07.684 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: AE
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: AE
2025-06-27 11:21:07.685 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-27 11:21:07.686 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: 83959850-cbf3-42cf-ac47-be4e87c0b8b5
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-06-27T11:21:07
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: AE
2025-06-27 11:21:07.751 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Forward flow xml
2025-06-27 11:21:08.063 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-27 11:21:08.063 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-27 11:21:08.063 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-27 11:21:08.063 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-27 11:21:08.064 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-27 11:21:08.064 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-27 11:21:08.066 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:21:08.066 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-27 11:21:08.066 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-27 11:21:08.067 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:21:08.067 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-27 11:21:08.068 [http-nio-8080-exec-1] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:21:08.068 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 11:21:08.068 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-27 11:21:08.072 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x019f27e5: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:21:08.072 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-27 11:21:08.073 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-27 11:21:08.074 [http-nio-8080-exec-1] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-27 11:21:08.075 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 11:21:08.075 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 11:21:09.033 [http-nio-8080-exec-1] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 902 milliseconds which is too long
2025-06-27 11:21:09.035 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:21:09.869 [http-nio-8080-exec-1] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-27 11:21:10.679 [http-nio-8080-exec-4] INFO  c.m.a.a.c.ReverseFlowController - 🔄 AS4 message endpoint called - redirecting to Phase4 servlet
2025-06-27 11:21:10.717 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - === AS4 Servlet: Processing incoming AS4 message ===
2025-06-27 11:21:10.717 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📨 Request URI: /reverse-flow/as4/
2025-06-27 11:21:10.717 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📋 Content-Type: multipart/related;    boundary="----=_Part_0_1167697449.1751003470057";    type="application/soap+xml"; charset=UTF-8
2025-06-27 11:21:10.718 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📏 Content-Length: 15159
2025-06-27 11:21:10.718 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Method: POST
2025-06-27 11:21:10.720 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📦 Received AS4 message, length: 15001
2025-06-27 11:21:10.721 [http-nio-8080-exec-3] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔍 Extracting MessageId from AS4 message
2025-06-27 11:21:10.722 [http-nio-8080-exec-3] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ Found MessageId: 8fa3d3de-8db7-4aa1-a310-503803c5eb0f@phase4
2025-06-27 11:21:10.722 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - === Processing AS4 message content ===
2025-06-27 11:21:10.722 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📦 AS4 message length: 15001
2025-06-27 11:21:10.755 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 AS4 message preview with header: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>83959850-cbf3-42cf-ac47-be4e87c0b8b5</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T11:21:07</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>busdox-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********02003</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-06-27 11:21:10.756 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Original UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>83959850-cbf3-42cf-ac47-be4e87c0b8b5</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T11:21:07</sh:CreationDateAndTime>
      </sh:DocumentIdentifica
2025-06-27 11:21:10.756 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Checking if UBL XML contains SBDH headers
2025-06-27 11:21:10.761 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📋 SBDH headers detected, extracting invoice document
2025-06-27 11:21:10.761 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Root element namespace: http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader
2025-06-27 11:21:10.762 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Found 1 Invoice nodes
2025-06-27 11:21:10.763 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Invoice node namespace: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2, local name: Invoice
2025-06-27 11:21:10.805 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔧 Converted node to XML string, length: 11967
2025-06-27 11:21:10.806 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-27 11:21:10.806 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted Invoice document from SBDH, length: 11967
2025-06-27 11:21:10.806 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted clean UBL document, length: 11967
2025-06-27 11:21:10.806 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Clean UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-27 11:21:10.806 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting country code from UBL XML
2025-06-27 11:21:10.809 [http-nio-8080-exec-3] WARN  c.m.a.c.s.Phase4AS4ReceiverService - ⚠️ No country code found in UBL XML, using DEFAULT
2025-06-27 11:21:10.809 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting document type from UBL XML
2025-06-27 11:21:10.811 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Detected document type: INVOICE from root element: Invoice
2025-06-27 11:21:10.811 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 🌍 Detected country: DEFAULT, document type: INVOICE
2025-06-27 11:21:10.812 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - Processing incoming UBL XML for country: DEFAULT and document type: INVOICE
2025-06-27 11:21:10.812 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - Parsing UBL XML to object using country-specific configuration: DEFAULT/INVOICE
2025-06-27 11:21:10.813 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 🔍 Using UBL class for parsing: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-06-27 11:21:10.814 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 UBL XML to parse (first 1000 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-27 11:21:10.814 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 Root element: <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
2025-06-27 11:21:10.814 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - ✅ Found expected root element: Invoice
2025-06-27 11:21:10.814 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating JAXB context for class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
2025-06-27 11:21:10.815 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-27 11:21:10.816 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-27 11:21:10.852 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-06-27 11:21:10.929 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-06-27 11:21:10.931 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-06-27 11:21:10.931 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-27 11:21:10.932 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-27 11:21:11.693 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 XML to unmarshal (first 500 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-27 11:21:11.727 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Successfully unmarshalled XML to object of type: jakarta.xml.bind.JAXBElement
2025-06-27 11:21:11.727 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 🔄 Handling response for DEFAULT/INVOICE with file writing as default
2025-06-27 11:21:11.733 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 📄 Response written to file: C:\Users\<USER>\backy\as4-main\responses\response_DEFAULT_INVOICE_20250627_112111_728.xml
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 📊 File size: 11967 bytes
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - Extracting sender ID from UBL document: JAXBElement
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - No sender ID found in UBL document
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] WARN  c.m.apsp.core.service.InvoiceService - ⚠️ No sender ID found in UBL document, cannot send response
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - ✅ Response successfully written to file and sent via REST
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - ✅ AS4 message processed successfully
2025-06-27 11:21:11.734 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 message processed successfully: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>

2025-06-27 11:21:11.843 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-27 11:21:11.843 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 44f69fb9-6aff-4feb-a232-72f33020e725
2025-06-27 11:21:11.843 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:21:11.844 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 4227 ms
2025-06-27 11:22:41.879 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-27 11:22:41.882 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-27 11:22:48.824 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 20832 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-27 11:22:48.827 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-27 11:22:48.828 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-27 11:22:51.443 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-27 11:22:51.444 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-27 11:22:55.766 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration...
2025-06-27 11:22:55.793 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-06-27 11:22:55.795 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-27 11:22:55.880 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-06-27 11:22:55.882 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-06-27 11:22:55.883 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:22:55.884 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:22:55.884 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:22:55.885 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-06-27 11:22:55.886 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:22:55.886 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:22:55.887 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 11:22:55.887 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-06-27 11:22:55.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:22:55.889 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:22:55.889 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 11:22:55.890 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-06-27 11:22:55.890 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:22:55.891 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:22:55.891 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 11:22:55.892 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 11:22:55.893 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:22:55.893 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 11:22:55.956 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-27 11:22:55.992 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-27 11:22:55.998 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-27 11:22:56.036 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-27 11:22:56.037 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-27 11:22:56.037 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 11:22:56.038 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:22:56.039 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-27 11:22:56.039 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-27 11:22:56.039 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-27 11:22:56.040 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-27 11:22:56.050 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-27 11:22:56.050 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-27 11:22:56.050 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-27 11:22:56.050 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-27 11:22:56.051 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:22:56.051 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-27 11:22:56.051 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-27 11:22:56.057 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-27 11:22:56.058 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-27 11:22:56.058 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-27 11:22:56.096 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-27 11:22:56.096 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-27 11:22:56.101 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-27 11:22:56.104 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:22:56.273 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-27 11:22:56.274 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-27 11:22:56.344 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 11:22:56.889 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-27 11:22:56.889 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-27 11:22:56.907 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.059 seconds (process running for 9.842)
2025-06-27 11:23:10.138 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-06-27 11:23:10.138 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-06-27 11:23:10.138 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-06-27 11:23:10.867 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 11:23:10.867 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-06-27 11:23:10.867 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 0
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 89cc92b4-c778-45ad-a59c-4855e2373f9d
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-27 11:23:10.886 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-27 11:23:10.904 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ Unknown document type: Invoice, defaulting to INVOICE
2025-06-27 11:23:10.908 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-27 11:23:10.908 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-27 11:23:10.909 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-27 11:23:10.909 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-27 11:23:10.940 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-27 11:23:10.941 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-27 11:23:10.941 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-27 11:23:10.941 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-27 11:23:10.945 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-27 11:23:10.947 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-27 11:23:10.947 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-27 11:23:10.949 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-27 11:23:10.950 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-27 11:23:10.950 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1751003590947
2025-06-27 11:23:10.950 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x6990ed85: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:23:10.952 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x00e53a3d: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:23:10.953 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x5b6ac8de: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-27 11:23:10.954 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x4ec4efad: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-27 11:23:10.954 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: AE
2025-06-27 11:23:10.955 [http-nio-8080-exec-1] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-27 11:23:10.955 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-27 11:23:10.955 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-27 11:23:10.955 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-27 11:23:10.955 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: AE
2025-06-27 11:23:10.956 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-27 11:23:10.957 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-27 11:23:11.040 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-27 11:23:11.040 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: 2fd99daf-b9f1-4523-a273-ce74d5296e7c
2025-06-27 11:23:11.041 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-06-27T11:23:10
2025-06-27 11:23:11.041 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-27 11:23:11.041 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-27 11:23:11.041 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: AE
2025-06-27 11:23:11.041 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - Forward flow xml : <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>2fd99daf-b9f1-4523-a273-ce74d5296e7c</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T11:23:10</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>busdox-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********02003</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-06-27 11:23:11.324 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-27 11:23:11.324 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-27 11:23:11.324 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-27 11:23:11.325 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-27 11:23:11.325 [http-nio-8080-exec-1] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-27 11:23:11.325 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-27 11:23:11.329 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 11:23:11.329 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-27 11:23:11.329 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-27 11:23:11.329 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:23:11.329 [http-nio-8080-exec-1] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-27 11:23:11.330 [http-nio-8080-exec-1] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:23:11.331 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 11:23:11.332 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-27 11:23:11.336 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x00e53a3d: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 11:23:11.337 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-27 11:23:11.337 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-27 11:23:11.339 [http-nio-8080-exec-1] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-27 11:23:11.339 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 11:23:11.339 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 11:23:12.390 [http-nio-8080-exec-1] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 979 milliseconds which is too long
2025-06-27 11:23:12.393 [http-nio-8080-exec-1] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:23:13.277 [http-nio-8080-exec-1] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-27 11:23:13.792 [http-nio-8080-exec-4] INFO  c.m.a.a.c.ReverseFlowController - 🔄 AS4 message endpoint called - redirecting to Phase4 servlet
2025-06-27 11:23:13.838 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - === AS4 Servlet: Processing incoming AS4 message ===
2025-06-27 11:23:13.839 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📨 Request URI: /reverse-flow/as4/
2025-06-27 11:23:13.839 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📋 Content-Type: multipart/related;    boundary="----=_Part_0_592957076.1751003593405";    type="application/soap+xml"; charset=UTF-8
2025-06-27 11:23:13.839 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📏 Content-Length: 15155
2025-06-27 11:23:13.839 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Method: POST
2025-06-27 11:23:13.841 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📦 Received AS4 message, length: 15020
2025-06-27 11:23:13.841 [http-nio-8080-exec-3] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔍 Extracting MessageId from AS4 message
2025-06-27 11:23:13.841 [http-nio-8080-exec-3] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ Found MessageId: 22492dc0-ce39-4d43-9fa7-57c4e00e06a4@phase4
2025-06-27 11:23:13.842 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - === Processing AS4 message content ===
2025-06-27 11:23:13.842 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📦 AS4 message length: 15020
2025-06-27 11:23:13.864 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 AS4 message preview with header: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>2fd99daf-b9f1-4523-a273-ce74d5296e7c</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T11:23:10</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>busdox-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********02003</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-06-27 11:23:13.866 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Original UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>2fd99daf-b9f1-4523-a273-ce74d5296e7c</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T11:23:10</sh:CreationDateAndTime>
      </sh:DocumentIdentifica
2025-06-27 11:23:13.866 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Checking if UBL XML contains SBDH headers
2025-06-27 11:23:13.872 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📋 SBDH headers detected, extracting invoice document
2025-06-27 11:23:13.872 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Root element namespace: http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader
2025-06-27 11:23:13.874 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Found 1 Invoice nodes
2025-06-27 11:23:13.874 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Invoice node namespace: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2, local name: Invoice
2025-06-27 11:23:13.917 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔧 Converted node to XML string, length: 11967
2025-06-27 11:23:13.917 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-27 11:23:13.918 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted Invoice document from SBDH, length: 11967
2025-06-27 11:23:13.918 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted clean UBL document, length: 11967
2025-06-27 11:23:13.918 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Clean UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-27 11:23:13.918 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting country code from UBL XML
2025-06-27 11:23:13.921 [http-nio-8080-exec-3] WARN  c.m.a.c.s.Phase4AS4ReceiverService - ⚠️ No country code found in UBL XML, using DEFAULT
2025-06-27 11:23:13.921 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting document type from UBL XML
2025-06-27 11:23:13.924 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Detected document type: INVOICE from root element: Invoice
2025-06-27 11:23:13.924 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 🌍 Detected country: DEFAULT, document type: INVOICE
2025-06-27 11:23:13.924 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - Processing incoming UBL XML for country: DEFAULT and document type: INVOICE
2025-06-27 11:23:13.925 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - Parsing UBL XML to object using country-specific configuration: DEFAULT/INVOICE
2025-06-27 11:23:13.927 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 🔍 Using UBL class for parsing: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-06-27 11:23:13.927 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 UBL XML to parse (first 1000 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-27 11:23:13.928 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 Root element: <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
2025-06-27 11:23:13.928 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - ✅ Found expected root element: Invoice
2025-06-27 11:23:13.929 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating JAXB context for class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
2025-06-27 11:23:13.929 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-27 11:23:13.930 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-27 11:23:13.979 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-06-27 11:23:14.051 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-06-27 11:23:14.052 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-06-27 11:23:14.053 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-27 11:23:14.054 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-27 11:23:14.916 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 XML to unmarshal (first 500 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-27 11:23:14.946 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Successfully unmarshalled XML to object of type: jakarta.xml.bind.JAXBElement
2025-06-27 11:23:14.946 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 🔄 Handling response for DEFAULT/INVOICE with file writing as default
2025-06-27 11:23:14.954 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 📄 Response written to file: C:\Users\<USER>\backy\as4-main\responses\response_DEFAULT_INVOICE_20250627_112314_947.xml
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - 📊 File size: 11967 bytes
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - Extracting sender ID from UBL document: JAXBElement
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] DEBUG c.m.apsp.core.service.InvoiceService - No sender ID found in UBL document
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] WARN  c.m.apsp.core.service.InvoiceService - ⚠️ No sender ID found in UBL document, cannot send response
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] INFO  c.m.apsp.core.service.InvoiceService - ✅ Response successfully written to file and sent via REST
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] INFO  c.m.a.c.s.Phase4AS4ReceiverService - ✅ AS4 message processed successfully
2025-06-27 11:23:14.955 [http-nio-8080-exec-3] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 message processed successfully: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>

2025-06-27 11:23:15.116 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-27 11:23:15.116 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 89cc92b4-c778-45ad-a59c-4855e2373f9d
2025-06-27 11:23:15.116 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-27 11:23:15.116 [http-nio-8080-exec-1] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 4230 ms
2025-06-27 11:23:29.400 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-27 11:23:29.404 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-27 12:31:44.324 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 17304 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-27 12:31:44.326 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-27 12:31:44.327 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-27 12:31:46.815 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-27 12:31:46.816 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-27 12:31:50.868 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration...
2025-06-27 12:31:50.904 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-06-27 12:31:50.905 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-27 12:31:51.006 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-06-27 12:31:51.008 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-06-27 12:31:51.009 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:31:51.009 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:31:51.009 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:31:51.010 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-06-27 12:31:51.010 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:31:51.010 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:31:51.010 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:31:51.010 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-06-27 12:31:51.010 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:31:51.010 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:31:51.011 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:31:51.011 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-06-27 12:31:51.011 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:31:51.011 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:31:51.011 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:31:51.012 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-06-27 12:31:51.012 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:31:51.012 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:31:51.012 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:31:51.012 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-27 12:31:51.012 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-06-27 12:31:51.012 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:31:51.013 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:31:51.076 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-27 12:31:51.128 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-27 12:31:51.132 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-27 12:31:51.162 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-27 12:31:51.162 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-27 12:31:51.162 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 12:31:51.163 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:31:51.163 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-27 12:31:51.163 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-27 12:31:51.164 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-27 12:31:51.164 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-27 12:31:51.170 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://***************:8080/suntec/as4
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-27 12:31:51.171 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-27 12:31:51.196 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-27 12:31:51.197 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-27 12:31:51.197 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-27 12:31:51.232 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-27 12:31:51.232 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-27 12:31:51.243 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-27 12:31:51.246 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:31:51.392 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-27 12:31:51.392 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-27 12:31:51.457 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 12:31:51.953 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-27 12:31:51.954 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-27 12:31:51.964 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 8.557 seconds (process running for 9.238)
2025-06-27 12:31:55.179 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-06-27 12:31:55.179 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-06-27 12:31:55.180 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-06-27 12:31:56.046 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 12:31:56.046 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-06-27 12:31:56.047 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-06-27 12:31:56.065 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 12:31:56.066 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 0
2025-06-27 12:31:56.067 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-27 12:31:56.068 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 39fc0909-8e08-4270-9c0e-4d867b5b8b16
2025-06-27 12:31:56.068 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-27 12:31:56.068 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-27 12:31:56.097 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ Unknown document type: Invoice, defaulting to INVOICE
2025-06-27 12:31:56.101 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-27 12:31:56.102 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-27 12:31:56.103 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-27 12:31:56.104 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-27 12:31:56.135 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-27 12:31:56.136 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-27 12:31:56.136 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-27 12:31:56.136 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-27 12:31:56.139 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-27 12:31:56.142 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-27 12:31:56.143 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-27 12:31:56.146 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-27 12:31:56.146 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-27 12:31:56.146 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1751007716143
2025-06-27 12:31:56.146 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x36da7f7b: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:31:56.147 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x06f1b686: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x294ae4d0: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x23af73e7: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: AE
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-27 12:31:56.148 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-27 12:31:56.149 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-27 12:31:56.149 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: AE
2025-06-27 12:31:56.149 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-27 12:31:56.149 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-27 12:31:56.213 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-27 12:31:56.213 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: b59a354b-cd53-4a80-bdb8-f99deb7d70ed
2025-06-27 12:31:56.213 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-06-27T12:31:56
2025-06-27 12:31:56.213 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-27 12:31:56.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-27 12:31:56.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: AE
2025-06-27 12:31:56.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Forward flow xml : <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>b59a354b-cd53-4a80-bdb8-f99deb7d70ed</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T12:31:56</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>busdox-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********02003</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-06-27 12:31:56.497 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-27 12:31:56.498 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-27 12:31:56.498 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-27 12:31:56.498 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-27 12:31:56.498 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-27 12:31:56.498 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-27 12:31:56.500 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:31:56.500 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-27 12:31:56.500 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-27 12:31:56.500 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://***************:8080/suntec/as4
2025-06-27 12:31:56.501 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-27 12:31:56.502 [http-nio-8080-exec-2] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://***************:8080/suntec/as4
2025-06-27 12:31:56.503 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 12:31:56.504 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-27 12:31:56.508 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x06f1b686: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:31:56.508 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-27 12:31:56.508 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-27 12:31:56.510 [http-nio-8080-exec-2] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-27 12:31:56.510 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 12:31:56.511 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 12:31:57.807 [http-nio-8080-exec-2] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 1236 milliseconds which is too long
2025-06-27 12:31:57.809 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://***************:8080/suntec/as4
2025-06-27 12:31:58.610 [http-nio-8080-exec-2] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-27 12:31:59.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-27 12:31:59.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 39fc0909-8e08-4270-9c0e-4d867b5b8b16
2025-06-27 12:31:59.375 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://***************:8080/suntec/as4
2025-06-27 12:31:59.375 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 3308 ms
2025-06-27 12:32:32.644 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-27 12:32:32.647 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-27 12:32:35.911 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 23164 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-27 12:32:35.913 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-27 12:32:35.914 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-27 12:32:38.413 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-27 12:32:38.414 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-27 12:32:42.732 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration...
2025-06-27 12:32:42.768 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-06-27 12:32:42.769 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-27 12:32:42.852 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-06-27 12:32:42.852 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-06-27 12:32:42.853 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:32:42.854 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:32:42.854 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:32:42.854 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-06-27 12:32:42.854 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:32:42.854 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:32:42.854 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-27 12:32:42.855 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-06-27 12:32:42.855 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:32:42.855 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:32:42.855 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-27 12:32:42.855 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-06-27 12:32:42.856 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:32:42.856 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:32:42.856 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-27 12:32:42.856 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-06-27 12:32:42.856 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:32:42.856 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:32:42.857 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-27 12:32:42.857 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-27 12:32:42.857 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-06-27 12:32:42.857 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:32:42.857 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-27 12:32:42.914 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-27 12:32:42.948 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-27 12:32:42.954 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-27 12:32:42.983 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-27 12:32:42.984 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-27 12:32:42.984 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 12:32:42.984 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:32:42.985 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-27 12:32:42.986 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-27 12:32:42.986 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-27 12:32:42.987 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-27 12:32:42.996 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-27 12:32:42.996 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-27 12:32:42.997 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-27 12:32:42.997 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-27 12:32:42.997 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://***************:8080/suntec/as4
2025-06-27 12:32:42.997 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-27 12:32:42.997 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-27 12:32:43.016 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-27 12:32:43.017 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-27 12:32:43.017 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-27 12:32:43.051 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-27 12:32:43.051 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-27 12:32:43.068 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-27 12:32:43.074 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:32:43.222 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-27 12:32:43.222 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-27 12:32:43.281 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 12:32:43.817 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-27 12:32:43.818 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-27 12:32:43.833 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 8.82 seconds (process running for 9.511)
2025-06-27 12:32:57.221 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-06-27 12:32:57.222 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-06-27 12:32:57.222 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-06-27 12:32:57.954 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 12:32:57.954 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-06-27 12:32:57.954 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-06-27 12:32:57.969 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-27 12:32:57.970 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 0
2025-06-27 12:32:57.970 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-27 12:32:57.970 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: f0c7978d-be17-43bd-835e-7ab682a5cac2
2025-06-27 12:32:57.970 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-27 12:32:57.970 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-27 12:32:57.986 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ Unknown document type: Invoice, defaulting to INVOICE
2025-06-27 12:32:57.992 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-27 12:32:57.992 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-27 12:32:57.993 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-27 12:32:57.994 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-27 12:32:58.037 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-27 12:32:58.038 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-27 12:32:58.039 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-27 12:32:58.040 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-27 12:32:58.047 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-27 12:32:58.050 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-27 12:32:58.051 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-27 12:32:58.054 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-27 12:32:58.055 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-27 12:32:58.055 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1751007778051
2025-06-27 12:32:58.055 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x40edc441: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:32:58.056 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x3a1c8d64: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:32:58.056 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x04416a46: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x74ad0263: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: AE
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: AE
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-27 12:32:58.057 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-27 12:32:58.121 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: 8e2eeca6-5234-4f11-97fc-a5adfbf3598e
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-06-27T12:32:58
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: AE
2025-06-27 12:32:58.122 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Forward flow xml : <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>8e2eeca6-5234-4f11-97fc-a5adfbf3598e</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-06-27T12:32:58</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>kush-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********02003</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-06-27 12:32:58.422 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-27 12:32:58.423 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-27 12:32:58.423 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-27 12:32:58.423 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-27 12:32:58.423 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-27 12:32:58.423 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-27 12:32:58.425 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-27 12:32:58.425 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-27 12:32:58.425 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-27 12:32:58.425 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://***************:8080/suntec/as4
2025-06-27 12:32:58.426 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-27 12:32:58.426 [http-nio-8080-exec-2] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://***************:8080/suntec/as4
2025-06-27 12:32:58.427 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 12:32:58.427 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-27 12:32:58.429 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x3a1c8d64: scheme=iso6523-actorid-upis; value=**********]
2025-06-27 12:32:58.430 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-27 12:32:58.430 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-27 12:32:58.431 [http-nio-8080-exec-2] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-27 12:32:58.432 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-27 12:32:58.432 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-27 12:32:59.373 [http-nio-8080-exec-2] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 883 milliseconds which is too long
2025-06-27 12:32:59.375 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://***************:8080/suntec/as4
2025-06-27 12:33:00.163 [http-nio-8080-exec-2] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-27 12:33:01.072 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-27 12:33:01.073 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: f0c7978d-be17-43bd-835e-7ab682a5cac2
2025-06-27 12:33:01.073 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://***************:8080/suntec/as4
2025-06-27 12:33:01.073 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 3103 ms
2025-06-27 12:54:16.247 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-27 12:54:16.252 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
