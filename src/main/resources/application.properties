# MoroHub AS4 Service Configuration
# ==================================

# Application Configuration
spring.application.name=morohub-as4-service
server.port=8080

# Active Profile (dummy for development, production for live)
spring.profiles.active=dummy

# AS4 Mode Configuration
as4.mode=dummy
as4.security.enabled=true
as4.validation.enabled=false
as4.certificate.validation.enabled=false

# AS4 Endpoint Configuration (direct endpoint for development/testing)
as4.endpoint.url=http://***************:8080/suntec/as4

invoice.class.path=oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
factory.class.path=oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory

# Decryption API Configuration
decrypt.api.host=localhost
decrypt.api.port=8090
logging.api.port=8090

# Response/Credit Note Configuration (for reverse flow)
response.class.path=oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType
response.factory.class.path=oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory
creditnote.class.path=oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType
creditnote.factory.class.path=oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory


# Keystore Configuration (for signing outgoing messages)
as4.keystore.path=keystore/cert.p12
as4.keystore.password=TFd20lJQ4f8j
as4.keystore.key.alias=cert
as4.keystore.key.password=TFd20lJQ4f8j

# Truststore Configuration (for verifying incoming messages)
as4.truststore.path=keystore/cert.p12
as4.truststore.password=TFd20lJQ4f8j

# Peppol Participant IDs
# These should match the participant IDs in your UBL invoice
peppol.sender.participant.id=9908:987654321
peppol.receiver.participant.id=9908:123456789

# File Configuration Paths
# These paths can be either classpath: prefixed or absolute/relative file paths
# If config folder exists, files will be loaded from there, otherwise from classpath
app.config.country-config.path=country-config.json
app.config.schematron.base-path=schematron/
app.config.keystore.base-path=keystore/

# Logging Configuration
logging.config=classpath:logback-spring.xml
logging.file.path=logs
logging.file.name=logs/phase4-as4.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# Database Configuration - PostgreSQL
spring.datasource.url=***********************************************
spring.datasource.username=commerz_usr
spring.datasource.password=commerz_usr
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true


# Log Levels
logging.level.com.helger.phase4=INFO
logging.level.com.morohub.apsp=INFO
logging.level.root=WARN
