package com.morohub.apsp.core.service;

import com.helger.peppolid.IDocumentTypeIdentifier;
import com.helger.peppolid.IParticipantIdentifier;
import com.helger.peppolid.IProcessIdentifier;
import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.security.cert.X509Certificate;
import java.io.InputStream;
import java.security.KeyStore;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Endpoint detail provider that bypasses certificate validation
 * Used for dummy/test modes where certificate validation should be skipped
 *
 * This provider returns null for the receiver AP certificate, which tells
 * Phase4PeppolSender to skip certificate validation entirely.
 */
public class TrustAllEndpointDetailProvider implements IAS4EndpointDetailProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(TrustAllEndpointDetailProvider.class);

    private final String endpointUrl;
    private final String keystorePath;
    private final String keystorePassword;
    private final String keyAlias;

    public TrustAllEndpointDetailProvider(String endpointUrl, String keystorePath, String keystorePassword, String keyAlias) {
        this.endpointUrl = endpointUrl;
        this.keystorePath = keystorePath;
        this.keystorePassword = keystorePassword;
        this.keyAlias = keyAlias;
        LOGGER.warn("⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: {}", endpointUrl);
        LOGGER.debug("Using keystore: {}, alias: {}", keystorePath, keyAlias);
    }

    @Override
    public void init(@Nonnull IDocumentTypeIdentifier docTypeID,
                     @Nonnull IProcessIdentifier processID,
                     @Nonnull IParticipantIdentifier receiverID) {
        LOGGER.debug("Initializing TrustAllEndpointDetailProvider for participant: {}", receiverID);
        // No initialization needed for trust-all provider
    }

    @Override
    @Nonnull
    public String getReceiverAPEndpointURL() {
        LOGGER.debug("Getting receiver AP endpoint URL: {}", endpointUrl);
        return endpointUrl;
    }

    @Override
    @Nullable
    public X509Certificate getReceiverAPCertificate() {
        // For dummy mode, load the same certificate from our keystore
        // This bypasses the "unsupported certificate issuer" error by using our own certificate
        LOGGER.debug("Loading dummy certificate for receiver AP to bypass validation");
        return loadDummyCertificateFromKeystore();
    }

    /**
     * Load the dummy certificate from our keystore for testing
     */
    private X509Certificate loadDummyCertificateFromKeystore() {
        try {
            LOGGER.debug("Loading dummy certificate from keystore for receiver AP");

            // Use configurable resource loading approach
            Resource keystoreResource = loadKeystoreResource(keystorePath);

            if (!keystoreResource.exists()) {
                LOGGER.warn("⚠️ Keystore not found at path '{}' for dummy certificate - receiver validation may fail", keystorePath);
                return null;
            }

            try (InputStream is = keystoreResource.getInputStream()) {
                KeyStore keystore = KeyStore.getInstance("PKCS12");
                keystore.load(is, keystorePassword.toCharArray());

                // Get the certificate using the configured alias
                X509Certificate cert = (X509Certificate) keystore.getCertificate(keyAlias);
                if (cert != null) {
                    LOGGER.info("✅ Dummy certificate loaded for receiver AP - using same cert as sender");
                    LOGGER.debug("Certificate subject: {}", cert.getSubjectDN());
                    LOGGER.debug("Using keystore: {}, alias: {}", keystorePath, keyAlias);
                    return cert;
                } else {
                    LOGGER.warn("⚠️ Certificate not found in keystore with alias '{}'", keyAlias);
                    return null;
                }
            }

        } catch (Exception e) {
            LOGGER.warn("⚠️ Failed to load dummy certificate: {}", e.getMessage());
            LOGGER.info("💡 This may cause certificate validation issues in dummy mode");
            return null;
        }
    }

    /**
     * Load keystore resource, checking config folder first, then classpath
     * This mimics the behavior of ConfigurableResourceLoader without requiring Spring injection
     */
    private Resource loadKeystoreResource(String keystorePath) {
        // Remove classpath: prefix if present
        String cleanPath = keystorePath.startsWith("classpath:") ?
            keystorePath.substring("classpath:".length()) : keystorePath;

        // First try to load from config folder
        Path configFilePath = Paths.get("config", cleanPath);
        File configFile = configFilePath.toFile();

        if (configFile.exists() && configFile.isFile()) {
            LOGGER.info("✅ Loading keystore from config folder: {}", configFilePath);
            return new FileSystemResource(configFile);
        }

        // Fallback to classpath
        ClassPathResource classpathResource = new ClassPathResource(cleanPath);
        if (classpathResource.exists()) {
            LOGGER.debug("📦 Loading keystore from classpath: {}", cleanPath);
            return classpathResource;
        }

        LOGGER.warn("⚠️ Keystore not found in config folder or classpath: {}", keystorePath);
        return classpathResource; // Return anyway, let caller handle the error
    }
}