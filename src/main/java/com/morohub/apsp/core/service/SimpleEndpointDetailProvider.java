package com.morohub.apsp.core.service;

import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import com.helger.phase4.model.pmode.leg.PModeLegProtocol;
import com.helger.peppolid.IParticipantIdentifier;
import com.helger.peppolid.IDocumentTypeIdentifier;
import com.helger.peppolid.IProcessIdentifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.security.cert.X509Certificate;
import java.security.KeyStore;
import java.io.InputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import com.morohub.apsp.config.ConfigurableResourceLoader;

/**
 * Simple endpoint detail provider for testing AS4 messages
 * Provides a direct endpoint URL without SMP discovery
 */
public class SimpleEndpointDetailProvider implements IAS4EndpointDetailProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleEndpointDetailProvider.class);

    @Autowired
    private ConfigurableResourceLoader resourceLoader;

    @Value("${app.config.keystore.base-path:keystore/}")
    private String keystoreBasePath;

    private final String endpointUrl;
    private final X509Certificate certificate;

    public SimpleEndpointDetailProvider(@Nonnull String endpointUrl) {
        this.endpointUrl = endpointUrl;
        this.certificate = loadCertificateFromKeystore();
        LOGGER.debug("Created SimpleEndpointDetailProvider for: {} with certificate: {}",
                    endpointUrl, certificate != null ? "loaded" : "none");
    }

    public SimpleEndpointDetailProvider(@Nonnull String endpointUrl, @Nullable X509Certificate certificate) {
        this.endpointUrl = endpointUrl;
        this.certificate = certificate;
        LOGGER.debug("Created SimpleEndpointDetailProvider for: {} with provided certificate", endpointUrl);
    }

    @Override
    public void init(@Nonnull IDocumentTypeIdentifier docTypeID,
                     @Nonnull IProcessIdentifier processID,
                     @Nonnull IParticipantIdentifier receiverID) {
        LOGGER.debug("Initializing endpoint detail provider for receiver: {}, docType: {}, process: {}",
                    receiverID, docTypeID, processID);
        // No initialization needed for simple provider
    }

    @Override
    @Nonnull
    public String getReceiverAPEndpointURL() {
        LOGGER.debug("Getting receiver AP endpoint URL: {}", endpointUrl);
        return endpointUrl;
    }

    @Override
    @Nullable
    public X509Certificate getReceiverAPCertificate() {
        LOGGER.debug("Getting receiver AP certificate: {}", certificate != null ? "available" : "none");
        return certificate;
    }

    private X509Certificate loadCertificateFromKeystore() {
        try {
            LOGGER.debug("Loading certificate from keystore for testing");

            // Try to load certificate from our test keystore using configurable resource loader
            Resource keystoreResource = resourceLoader.loadResource(keystoreBasePath, "as4-keystore.p12");
            if (!keystoreResource.exists()) {
                LOGGER.warn("Keystore not found at: {}, AS4 will work without certificate validation",
                    resourceLoader.getResourceLocation(keystoreBasePath + "as4-keystore.p12"));
                return null;
            }

            try (InputStream is = keystoreResource.getInputStream()) {
                KeyStore keystore = KeyStore.getInstance("PKCS12");
                keystore.load(is, "changeit".toCharArray());

                // Get the certificate from the keystore
                X509Certificate cert = (X509Certificate) keystore.getCertificate("as4-test");
                if (cert != null) {
                    LOGGER.info("✅ Certificate loaded from keystore for AS4 testing");
                    LOGGER.debug("Certificate subject: {}", cert.getSubjectDN());
                } else {
                    LOGGER.warn("⚠️ Certificate not found in keystore with alias 'as4-test'");
                }
                return cert;
            }

        } catch (Exception e) {
            LOGGER.warn("⚠️ Failed to load certificate from keystore: {}", e.getMessage());
            LOGGER.info("💡 AS4 will work without certificate validation for testing");
            return null;
        }
    }
}
