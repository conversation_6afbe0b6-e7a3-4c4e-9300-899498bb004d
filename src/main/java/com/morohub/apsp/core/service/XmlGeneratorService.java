package com.morohub.apsp.core.service;

import com.morohub.apsp.config.CountryConfigurationService;
import jakarta.xml.bind.JAXBElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


/**
 * Service for generating XML documents using dynamic class loading
 */
@Service
public class XmlGeneratorService {

    private static final Logger logger = LoggerFactory.getLogger(XmlGeneratorService.class);

    @Autowired
    private Environment env;

    @Autowired
    private XmlJsonConversionService xmlJsonConversionService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    /**
     * Generate UBL Invoice XML from request object using dynamic class loading
     */
    public <T> String generateInvoice(Object invoice) throws Exception {
        try {
            logger.debug("Generating invoice XML using dynamic class loading");

            String invoiceClassName = env.getProperty("invoice.class.path");
            String factoryClassPath = env.getProperty("factory.class.path");

            if (invoiceClassName == null || factoryClassPath == null) {
                throw new IllegalStateException("Properties 'invoice.class.path' or 'factory.class.path' not set");
            }

            Class<T> invoiceClass = (Class<T>) Class.forName(invoiceClassName);
            Class<?> factoryClass = Class.forName(factoryClassPath);

            if (!invoiceClass.isInstance(invoice)) {
                throw new IllegalArgumentException("Invoice object is not of type " + invoiceClassName);
            }

            T typedInvoice = invoiceClass.cast(invoice);
            Object factoryInstance = factoryClass.getDeclaredConstructor().newInstance();
            java.lang.reflect.Method createInvoiceMethod = factoryClass.getMethod("createInvoice", invoiceClass);
            JAXBElement<T> jaxbElement = (JAXBElement<T>) createInvoiceMethod.invoke(factoryInstance, typedInvoice);

            return xmlJsonConversionService.objectToXml(jaxbElement, invoiceClass);

        } catch (Exception e) {
            logger.error("Error generating invoice XML using dynamic class loading", e);
            throw new RuntimeException("Failed to generate invoice XML: " + e.getMessage(), e);
        }
    }

    /**
     * Generate UBL Invoice XML using country-specific configuration
     */
    public <T> String generateInvoiceWithConfig(Object invoice, String countryCode, String documentType) throws Exception {
        try {
            logger.debug("Generating invoice XML using country-specific configuration: {}/{}", countryCode, documentType);

            // Get country-specific class configuration
            Class<T> invoiceClass = (Class<T>) countryConfigurationService.getUBLClass(countryCode, documentType);
            Class<?> factoryClass = countryConfigurationService.getFactoryClass(countryCode, documentType);

            logger.info("Using classes for {}/{}: {} and {}",
                countryCode, documentType, invoiceClass.getName(), factoryClass.getName());

            if (!invoiceClass.isInstance(invoice)) {
                throw new IllegalArgumentException("Invoice object is not of type " + invoiceClass.getName());
            }

            T typedInvoice = invoiceClass.cast(invoice);
            Object factoryInstance = factoryClass.getDeclaredConstructor().newInstance();

            // Determine the factory method name based on document type
            String methodName = getFactoryMethodName(documentType);
            java.lang.reflect.Method createMethod = factoryClass.getMethod(methodName, invoiceClass);
            JAXBElement<T> jaxbElement = (JAXBElement<T>) createMethod.invoke(factoryInstance, typedInvoice);

            String xml = xmlJsonConversionService.objectToXml(jaxbElement, invoiceClass);
            logger.info("Successfully generated XML for {}/{}", countryCode, documentType);
            return xml;

        } catch (Exception e) {
            logger.error("Failed to generate invoice XML for {}/{}", countryCode, documentType, e);
            throw new RuntimeException("Country-specific invoice XML generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Get the appropriate factory method name based on document type
     */
    private String getFactoryMethodName(String documentType) {
        switch (documentType.toUpperCase()) {
            case "INVOICE":
                return "createInvoice";
            case "CREDITNOTE":
                return "createCreditNote";
            case "APPLICATIONRESPONSE":
                return "createApplicationResponse";
            default:
                throw new IllegalArgumentException("Unsupported document type: " + documentType);
        }
    }

    /**
     * Generate invoice response XML for reverse flow using dynamic class loading
     * @deprecated Use generateInvoiceResponseXmlWithConfig instead
     */


    /**
     * Generate invoice response XML with country-specific configuration
     */
    public String generateInvoiceResponseXmlWithConfig(Object incomingDocument, String countryCode, String documentType) throws Exception {
        try {
            logger.debug("Generating invoice response XML with country-specific configuration: {}/{}", countryCode, documentType);

            // For response, we typically generate an ApplicationResponse
            String responseDocumentType = "APPLICATIONRESPONSE";

            // Get country-specific class configuration for response
            Class<?> responseClass = countryConfigurationService.getUBLClass(countryCode, responseDocumentType);
            Class<?> factoryClass = countryConfigurationService.getFactoryClass(countryCode, responseDocumentType);

            logger.info("Using response classes for {}/{}: {} and {}",
                countryCode, responseDocumentType, responseClass.getName(), factoryClass.getName());

            // Create a simple ApplicationResponse acknowledging the incoming document
            Object responseDocument = createApplicationResponse(incomingDocument, responseClass);

            Object factoryInstance = factoryClass.getDeclaredConstructor().newInstance();
            String methodName = getFactoryMethodName(responseDocumentType);
            java.lang.reflect.Method createMethod = factoryClass.getMethod(methodName, responseClass);
            JAXBElement<?> jaxbElement = (JAXBElement<?>) createMethod.invoke(factoryInstance, responseDocument);

            String xml = xmlJsonConversionService.objectToXml(jaxbElement, responseClass);
            logger.info("Successfully generated response XML for {}/{}", countryCode, documentType);
            return xml;

        } catch (Exception e) {
            logger.error("Failed to generate response XML for {}/{}", countryCode, documentType, e);
            // Fall back to default response
            return generateDefaultResponse(incomingDocument);
        }
    }

    /**
     * Create a simple ApplicationResponse for the incoming document
     */
    private Object createApplicationResponse(Object incomingDocument, Class<?> responseClass) throws Exception {
        try {
            // Create new instance of ApplicationResponse
            Object response = responseClass.getDeclaredConstructor().newInstance();

            // Set basic fields using reflection
            setFieldValue(response, "setID", "RESP-" + System.currentTimeMillis());
            setFieldValue(response, "setIssueDate", java.time.LocalDate.now());
            setFieldValue(response, "setIssueTime", java.time.LocalTime.now());

            logger.debug("Created basic ApplicationResponse with ID and timestamps");
            return response;

        } catch (Exception e) {
            logger.error("Failed to create ApplicationResponse", e);
            throw e;
        }
    }

    /**
     * Set field value using reflection
     */
    private void setFieldValue(Object object, String methodName, Object value) {
        try {
            java.lang.reflect.Method method = findMethodByName(object.getClass(), methodName);
            if (method != null) {
                method.invoke(object, value);
                logger.debug("Set field {} to value: {}", methodName, value);
            } else {
                logger.debug("Method {} not found in class {}", methodName, object.getClass().getSimpleName());
            }
        } catch (Exception e) {
            logger.debug("Failed to set field {} to value {}: {}", methodName, value, e.getMessage());
        }
    }

    /**
     * Find method by name
     */
    private java.lang.reflect.Method findMethodByName(Class<?> clazz, String methodName) {
        for (java.lang.reflect.Method method : clazz.getMethods()) {
            if (method.getName().equals(methodName) && method.getParameterCount() == 1) {
                return method;
            }
        }
        return null;
    }

    /**
     * Generate default response when dynamic classes are not available
     */
    private String generateDefaultResponse(Object originalInvoice) {
        logger.info("Generating default response XML");

        String responseId = "RESP-" + System.currentTimeMillis();
        String currentDate = java.time.LocalDate.now().toString();

        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
               "<ApplicationResponse xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2\">\n" +
               "    <ID>" + responseId + "</ID>\n" +
               "    <IssueDate>" + currentDate + "</IssueDate>\n" +
               "    <ResponseDate>" + currentDate + "</ResponseDate>\n" +
               "    <Note>Invoice received and processed successfully</Note>\n" +
               "</ApplicationResponse>";
    }

}
