package com.morohub.apsp.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Utility service for loading resources from either config folder or classpath
 * Supports Spring Boot's config folder convention where external config files
 * in the config/ folder take precedence over classpath resources
 */
@Component
public class ConfigurableResourceLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigurableResourceLoader.class);
    
    private static final String CONFIG_FOLDER = "config";

    /**
     * Load a resource, checking config folder first, then classpath
     * 
     * @param resourcePath The path to the resource (relative to config folder or classpath)
     * @return Resource object that can be used to get InputStream
     */
    public Resource loadResource(String resourcePath) {
        LOGGER.debug("Loading resource: {}", resourcePath);
        
        // Remove classpath: prefix if present
        String cleanPath = resourcePath.startsWith("classpath:") ? 
            resourcePath.substring("classpath:".length()) : resourcePath;
        
        // First try to load from config folder
        Path configFilePath = Paths.get(CONFIG_FOLDER, cleanPath);
        File configFile = configFilePath.toFile();
        
        if (configFile.exists() && configFile.isFile()) {
            LOGGER.info("✅ Loading resource from config folder: {}", configFilePath);
            return new FileSystemResource(configFile);
        }
        
        // Fallback to classpath
        ClassPathResource classpathResource = new ClassPathResource(cleanPath);
        if (classpathResource.exists()) {
            LOGGER.debug("📦 Loading resource from classpath: {}", cleanPath);
            return classpathResource;
        }
        
        LOGGER.warn("⚠️ Resource not found in config folder or classpath: {}", resourcePath);
        return classpathResource; // Return anyway, let caller handle the error
    }

    /**
     * Load a resource with a base path, checking config folder first, then classpath
     * 
     * @param basePath Base path (e.g., "keystore/", "schematron/")
     * @param fileName File name within the base path
     * @return Resource object that can be used to get InputStream
     */
    public Resource loadResource(String basePath, String fileName) {
        String fullPath = basePath.endsWith("/") ? basePath + fileName : basePath + "/" + fileName;
        return loadResource(fullPath);
    }

    /**
     * Get InputStream for a resource, checking config folder first, then classpath
     * 
     * @param resourcePath The path to the resource
     * @return InputStream for the resource
     * @throws Exception if resource cannot be loaded
     */
    public InputStream getResourceAsStream(String resourcePath) throws Exception {
        Resource resource = loadResource(resourcePath);
        if (!resource.exists()) {
            throw new RuntimeException("Resource not found: " + resourcePath);
        }
        return resource.getInputStream();
    }

    /**
     * Get InputStream for a resource with base path
     * 
     * @param basePath Base path (e.g., "keystore/", "schematron/")
     * @param fileName File name within the base path
     * @return InputStream for the resource
     * @throws Exception if resource cannot be loaded
     */
    public InputStream getResourceAsStream(String basePath, String fileName) throws Exception {
        return getResourceAsStream(basePath.endsWith("/") ? basePath + fileName : basePath + "/" + fileName);
    }

    /**
     * Check if a resource exists, checking config folder first, then classpath
     * 
     * @param resourcePath The path to the resource
     * @return true if resource exists
     */
    public boolean resourceExists(String resourcePath) {
        Resource resource = loadResource(resourcePath);
        return resource.exists();
    }

    /**
     * Check if a resource exists with base path
     * 
     * @param basePath Base path (e.g., "keystore/", "schematron/")
     * @param fileName File name within the base path
     * @return true if resource exists
     */
    public boolean resourceExists(String basePath, String fileName) {
        return resourceExists(basePath.endsWith("/") ? basePath + fileName : basePath + "/" + fileName);
    }

    /**
     * Get the actual location of a resource for logging purposes
     * 
     * @param resourcePath The path to the resource
     * @return String describing where the resource was found
     */
    public String getResourceLocation(String resourcePath) {
        String cleanPath = resourcePath.startsWith("classpath:") ? 
            resourcePath.substring("classpath:".length()) : resourcePath;
        
        Path configFilePath = Paths.get(CONFIG_FOLDER, cleanPath);
        File configFile = configFilePath.toFile();
        
        if (configFile.exists() && configFile.isFile()) {
            return "config folder: " + configFilePath.toAbsolutePath();
        }
        
        ClassPathResource classpathResource = new ClassPathResource(cleanPath);
        if (classpathResource.exists()) {
            return "classpath: " + cleanPath;
        }
        
        return "not found: " + resourcePath;
    }
}
