package com.morohub.apsp.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morohub.apsp.config.model.CountryConfig;
import com.morohub.apsp.config.model.DocumentTypeConfig;
import com.morohub.apsp.config.model.MultiCountryConfig;
import com.morohub.apsp.config.model.XmlParsingConfig;
import com.morohub.apsp.config.model.SbdhConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Unified service to manage country-specific configurations for XML parsing, AS4 processing, and UBL document handling
 * Consolidates functionality from both CountryConfigurationService and MultiCountryConfigService
 */
@Service
public class CountryConfigurationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CountryConfigurationService.class);

    @Autowired
    private ObjectMapper objectMapper;

    private JsonNode configRoot;
    private JsonNode globalDefaults;
    private JsonNode countriesConfig;
    private MultiCountryConfig structuredConfig;

    @PostConstruct
    public void loadConfiguration() {
        try {
            LOGGER.info("Loading unified country configuration...");

            ClassPathResource resource = new ClassPathResource("country-config.json");
            if (!resource.exists()) {
                throw new RuntimeException("Configuration file 'country-config.json' not found in classpath");
            }

            try (InputStream inputStream = resource.getInputStream()) {
                // Load as JsonNode for flexible access
                configRoot = objectMapper.readTree(inputStream);
                globalDefaults = configRoot.get("globalDefaults");
                countriesConfig = configRoot.get("countries");

                LOGGER.info("✅ Unified country configuration loaded successfully");
                LOGGER.info("📊 Available countries: {}", String.join(", ", getAvailableCountries()));
            }

            // Load structured config separately
            try (InputStream inputStream2 = resource.getInputStream()) {
                structuredConfig = objectMapper.readValue(inputStream2, MultiCountryConfig.class);
                logConfigurationSummary();
            }

        } catch (IOException e) {
            LOGGER.error("❌ Failed to load country configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to load country configuration", e);
        }
    }
    
    /**
     * Get country and document-specific XML parsing configuration
     */
    public XmlParsingConfig getXmlParsingConfig(String countryCode, String documentType) {
        CountryConfig countryConfig = getCountryConfigModel(countryCode);
        DocumentTypeConfig docTypeConfig = countryConfig.getDocumentType(documentType);

        if (docTypeConfig == null) {
            throw new IllegalArgumentException("Document type " + documentType + " not found for country: " + countryCode);
        }

        return new XmlParsingConfig(globalDefaults, countryConfig, docTypeConfig);
    }

    /**
     * Get country-specific configuration using model classes
     */
    public CountryConfig getCountryConfigModel(String countryCode) {
        if (structuredConfig == null) {
            throw new IllegalStateException("Structured configuration not loaded");
        }

        CountryConfig countryConfig = structuredConfig.getCountry(countryCode);
        if (countryConfig == null) {
            LOGGER.warn("⚠️ Country {} not found, using DEFAULT configuration", countryCode);
            countryConfig = structuredConfig.getCountry("DEFAULT");
        }

        if (countryConfig == null) {
            throw new IllegalStateException("DEFAULT country configuration not found");
        }

        return countryConfig;
    }

    /**
     * Get document type configuration for a specific country and document type
     */
    public DocumentTypeConfig getDocumentTypeConfig(String countryCode, String documentType) {
        CountryConfig countryConfig = getCountryConfigModel(countryCode);
        DocumentTypeConfig docConfig = countryConfig.getDocumentType(documentType);

        if (docConfig == null) {
            throw new IllegalArgumentException("Document type " + documentType + " not found for country: " + countryCode);
        }

        return docConfig;
    }
    
    /**
     * Get available countries
     */
    public List<String> getAvailableCountries() {
        List<String> countries = new ArrayList<>();
        if (countriesConfig != null) {
            countriesConfig.fieldNames().forEachRemaining(countries::add);
        }
        return countries;
    }

    // ========== Methods from MultiCountryConfigService ==========

    /**
     * Get document type configuration for a specific country and document type (structured approach)
     * This method provides the same functionality as MultiCountryConfigService.getDocumentTypeConfig
     */
    public DocumentTypeConfig getDocumentTypeConfigStructured(String countryCode, String documentType) {
        return getDocumentTypeConfig(countryCode, documentType);
    }

    /**
     * Get UBL class for a specific country and document type
     */
    public Class<?> getUBLClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return Class.forName(config.getClassPath());
    }

    /**
     * Get factory class for a specific country and document type
     */
    public Class<?> getFactoryClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return Class.forName(config.getFactoryPath());
    }

    /**
     * Check if country and document type combination is supported
     */
    public boolean isSupported(String countryCode, String documentType) {
        if (structuredConfig == null) {
            return false;
        }
        return structuredConfig.supportsDocumentType(countryCode, documentType) ||
               structuredConfig.supportsDocumentType("DEFAULT", documentType);
    }

    /**
     * Get all supported countries
     */
    public Set<String> getSupportedCountries() {
        if (structuredConfig == null || structuredConfig.getCountries() == null) {
            return java.util.Collections.emptySet();
        }
        return structuredConfig.getCountries().keySet();
    }

    /**
     * Reload configuration (useful for testing or runtime updates)
     */
    public void reloadConfiguration() {
        LOGGER.info("Reloading unified country configuration...");
        loadConfiguration();
    }

    /**
     * Get the full structured configuration (for debugging/admin purposes)
     */
    public MultiCountryConfig getFullConfiguration() {
        return structuredConfig;
    }

    /**
     * Get all supported document types for a country
     */
    public Set<String> getSupportedDocumentTypes(String countryCode) {
        if (structuredConfig == null) {
            return java.util.Collections.emptySet();
        }

        CountryConfig countryConfig = structuredConfig.getCountry(countryCode);
        if (countryConfig == null || countryConfig.getDocumentTypes() == null) {
            return java.util.Collections.emptySet();
        }

        return countryConfig.getDocumentTypes().keySet();
    }

    /**
     * Get schematron file for a specific country and document type (backward compatibility)
     */
    public String getSchematronFile(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getSchematronFile();
    }

    /**
     * Get schematron files for a specific country and document type
     */
    public List<String> getSchematronFiles(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getSchematronFiles();
    }

    /**
     * Get customization ID for a specific country and document type
     */
    public String getCustomizationId(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getCustomizationId();
    }

    /**
     * Get profile ID for a specific country and document type
     */
    public String getProfileId(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getProfileId();
    }
    

    

    

    


    /**
     * Log configuration summary for debugging
     */
    private void logConfigurationSummary() {
        if (structuredConfig == null || structuredConfig.getCountries() == null) {
            LOGGER.warn("No countries configured");
            return;
        }

        LOGGER.info("📋 Configuration Summary:");
        structuredConfig.getCountries().forEach((countryCode, countryConfig) -> {
            LOGGER.info("  🌍 {} ({}): {} document types",
                countryCode,
                countryConfig.getName(),
                countryConfig.getDocumentTypes() != null ? countryConfig.getDocumentTypes().size() : 0);

            if (countryConfig.getDocumentTypes() != null) {
                countryConfig.getDocumentTypes().forEach((docType, docConfig) -> {
                    LOGGER.debug("    📄 {}: {} | {}",
                        docType,
                        docConfig.getClassPath(),
                        docConfig.getSchematronFile());
                });
            }
        });
    }
}
