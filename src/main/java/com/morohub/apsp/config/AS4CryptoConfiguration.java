package com.morohub.apsp.config;

import com.helger.phase4.crypto.AS4CryptoFactoryInMemoryKeyStore;
import com.helger.phase4.crypto.AS4CryptoFactoryProperties;
import com.helger.phase4.crypto.IAS4CryptoFactory;
import com.helger.phase4.peppol.Phase4PeppolSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.InputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;

/**
 * Specialized crypto configuration for AS4/Phase4
 * Handles keystore loading and crypto factory setup
 */
@Configuration
public class AS4CryptoConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(AS4CryptoConfiguration.class);

    @Value("${as4.mode:dummy}")
    private String as4Mode;

    @Value("${as4.security.enabled:true}")
    private boolean securityEnabled;

    @Value("${as4.keystore.path:keystore/cert.p12}")
    private String keystorePath;

    @Value("${as4.keystore.password:TFd20lJQ4f8j}")
    private String keystorePassword;

    @Value("${as4.keystore.key.alias:cert}")
    private String keyAlias;

    @Value("${as4.keystore.key.password:TFd20lJQ4f8j}")
    private String keyPassword;

    @Value("${as4.truststore.path:keystore/cert.p12}")
    private String truststorePath;

    @Value("${as4.truststore.password:TFd20lJQ4f8j}")
    private String truststorePassword;

    /**
     * Create a configured AS4 crypto factory
     */
    @Bean
    public IAS4CryptoFactory configuredAS4CryptoFactory() {
        LOGGER.info("=== Creating Configured AS4 Crypto Factory ===");

        if (!securityEnabled) {
            LOGGER.warn("⚠️ Security disabled - returning default crypto factory");
            return AS4CryptoFactoryProperties.getDefaultInstance();
        }

        try {
            // Check keystore availability
            ClassPathResource keystoreResource = new ClassPathResource(keystorePath);
            if (!keystoreResource.exists()) {
                LOGGER.warn("⚠️ Keystore not found: {} - using default crypto factory", keystorePath);
                return AS4CryptoFactoryProperties.getDefaultInstance();
            }

            // Load keystore using Phase4 utilities
            KeyStore keyStore = loadKeystore();

            // Load truststore if available
            KeyStore trustStore = null;
            ClassPathResource truststoreResource = new ClassPathResource(truststorePath);
            if (truststoreResource.exists()) {
                trustStore = loadTruststore();
                LOGGER.info("✅ Truststore loaded: {}", truststorePath);
            } else {
                LOGGER.info("ℹ️ No truststore configured, using keystore for trust");
                trustStore = keyStore; // Use keystore as truststore if no separate truststore
            }

            // Create in-memory crypto factory with loaded keystores
            AS4CryptoFactoryInMemoryKeyStore cryptoFactory = new AS4CryptoFactoryInMemoryKeyStore(
                keyStore,
                keyAlias,
                keyPassword,
                trustStore
            );

            // Note: The crypto factory will be used when this bean is injected
            // Phase4 will use this factory when the builder is configured

            // Validate the configuration
            validateCryptoConfiguration();

            LOGGER.info("✅ AS4 Crypto Factory configured successfully and set as default");
            LOGGER.info("📁 Keystore: {}", keystorePath);
            LOGGER.info("🔑 Key Alias: {}", keyAlias);
            LOGGER.info("🛡️ Truststore: {}", truststoreResource.exists() ? truststorePath : "using keystore");

            return cryptoFactory;

        } catch (Exception e) {
            LOGGER.error("❌ Failed to configure AS4 crypto factory: {}", e.getMessage(), e);

            if ("production".equals(as4Mode)) {
                throw new RuntimeException("Crypto factory configuration is mandatory for production mode", e);
            } else {
                LOGGER.warn("⚠️ Using default crypto factory in {} mode", as4Mode);
                return AS4CryptoFactoryProperties.getDefaultInstance();
            }
        }
    }

    /**
     * Load keystore using standard Java KeyStore API
     */
    private KeyStore loadKeystore() throws Exception {
        ClassPathResource keystoreResource = new ClassPathResource(keystorePath);
        try (InputStream is = keystoreResource.getInputStream()) {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            keystore.load(is, keystorePassword.toCharArray());
            return keystore;
        }
    }

    /**
     * Load truststore using standard Java KeyStore API
     */
    private KeyStore loadTruststore() throws Exception {
        ClassPathResource truststoreResource = new ClassPathResource(truststorePath);
        try (InputStream is = truststoreResource.getInputStream()) {
            KeyStore truststore = KeyStore.getInstance("PKCS12");
            truststore.load(is, truststorePassword.toCharArray());
            return truststore;
        }
    }

    /**
     * Validate crypto configuration by testing keystore access
     */
    private void validateCryptoConfiguration() throws Exception {
        LOGGER.debug("Validating crypto configuration...");

        ClassPathResource keystoreResource = new ClassPathResource(keystorePath);
        try (InputStream is = keystoreResource.getInputStream()) {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            keystore.load(is, keystorePassword.toCharArray());

            // Check if alias exists
            if (!keystore.containsAlias(keyAlias)) {
                throw new Exception("Key alias '" + keyAlias + "' not found in keystore");
            }

            // Test private key access
            PrivateKey privateKey = (PrivateKey) keystore.getKey(keyAlias, keyPassword.toCharArray());
            if (privateKey == null) {
                throw new Exception("Private key not found for alias: " + keyAlias);
            }

            // Test certificate access
            X509Certificate certificate = (X509Certificate) keystore.getCertificate(keyAlias);
            if (certificate == null) {
                throw new Exception("Certificate not found for alias: " + keyAlias);
            }

            LOGGER.info("✅ Crypto configuration validation successful");
            LOGGER.debug("🔑 Private key algorithm: {}", privateKey.getAlgorithm());
            LOGGER.debug("📜 Certificate subject: {}", certificate.getSubjectDN());
            LOGGER.debug("📅 Certificate valid from: {} to: {}",
                certificate.getNotBefore(), certificate.getNotAfter());
        }
    }

    /**
     * Configure Phase4PeppolSender with crypto settings
     */
    public Phase4PeppolSender.SBDHBuilder configurePhase4PeppolSender(Phase4PeppolSender.SBDHBuilder builder) {
        LOGGER.debug("Configuring Phase4PeppolSender with crypto settings for mode: {}", as4Mode);

        if (!securityEnabled) {
            LOGGER.debug("Security disabled - no crypto configuration applied to sender");
            return builder;
        }

        try {
            // The crypto factory is configured as a Spring bean and will be used automatically
            LOGGER.debug("✅ Using configured crypto factory for signing");
            LOGGER.debug("🔑 Crypto factory will use key alias: {}", keyAlias);

            // Additional configuration based on mode
            if ("dummy".equals(as4Mode)) {
                LOGGER.debug("🧪 DUMMY MODE: Configuring sender for testing with relaxed validation");

            } else if ("production".equals(as4Mode)) {
                LOGGER.debug("🏭 PRODUCTION MODE: Configuring sender with strict validation");

            } else {
                LOGGER.debug("🧪 TEST MODE: Configuring sender for testing");
            }

            LOGGER.debug("✅ Phase4PeppolSender configured with crypto settings");
            return builder;

        } catch (Exception e) {
            LOGGER.error("❌ Failed to configure Phase4PeppolSender crypto settings: {}", e.getMessage(), e);

            if ("production".equals(as4Mode)) {
                throw new RuntimeException("Failed to configure sender crypto settings for production", e);
            } else {
                LOGGER.warn("⚠️ Continuing without sender crypto configuration in {} mode", as4Mode);
                return builder;
            }
        }
    }

    /**
     * Get keystore information for diagnostics
     */
    public String getKeystoreInfo() {
        try {
            ClassPathResource keystoreResource = new ClassPathResource(keystorePath);
            if (!keystoreResource.exists()) {
                return "Keystore not found: " + keystorePath;
            }

            try (InputStream is = keystoreResource.getInputStream()) {
                KeyStore keystore = KeyStore.getInstance("PKCS12");
                keystore.load(is, keystorePassword.toCharArray());

                X509Certificate cert = (X509Certificate) keystore.getCertificate(keyAlias);
                if (cert != null) {
                    return String.format(
                        "Keystore: %s, Alias: %s, Subject: %s, Valid: %s to %s",
                        keystorePath,
                        keyAlias,
                        cert.getSubjectDN(),
                        cert.getNotBefore(),
                        cert.getNotAfter()
                    );
                } else {
                    return "Certificate not found for alias: " + keyAlias;
                }
            }
        } catch (Exception e) {
            return "Error reading keystore: " + e.getMessage();
        }
    }

    /**
     * Check if crypto configuration is properly set up
     */
    public boolean isCryptoConfigured() {
        if (!securityEnabled) {
            return false;
        }

        try {
            ClassPathResource keystoreResource = new ClassPathResource(keystorePath);
            if (!keystoreResource.exists()) {
                return false;
            }

            try (InputStream is = keystoreResource.getInputStream()) {
                KeyStore keystore = KeyStore.getInstance("PKCS12");
                keystore.load(is, keystorePassword.toCharArray());
                
                return keystore.containsAlias(keyAlias) && 
                       keystore.getKey(keyAlias, keyPassword.toCharArray()) != null;
            }
        } catch (Exception e) {
            LOGGER.debug("Crypto configuration check failed: {}", e.getMessage());
            return false;
        }
    }
}
