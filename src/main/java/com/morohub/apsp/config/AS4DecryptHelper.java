package com.morohub.apsp.config;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.spec.MGF1ParameterSpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.zip.GZIPInputStream;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

@Service
public class AS4DecryptHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(AS4DecryptHelper.class);

    @Autowired
    private KeystoreProperties keystoreProperties;

    @Autowired
    private ConfigurableResourceLoader resourceLoader;

    @Value("${app.config.keystore.base-path:keystore/}")
    private String keystoreBasePath;
    // Keystore configuration values


    public  String testAS4Decryption(byte[] as4MessageBytes) throws Exception {
        String as4MessageContent = new String(as4MessageBytes, StandardCharsets.ISO_8859_1);

        // Step 1: Parse MIME boundary
        String boundary = null;
        for (String line : as4MessageContent.split("\r?\n")) {
            if (line.startsWith("--")) {
                boundary = line.trim();
                break;
            }
        }
        if (boundary == null) throw new RuntimeException("❌ MIME boundary not found");

        String[] parts = as4MessageContent.split(boundary.replace("-", "\\-").replace("+", "\\+").replace(".", "\\."));
        String soapEnvelope = null;
        byte[] attachmentBytes = null;

        // Step 2: Extract SOAP envelope and binary payload
        for (String part : parts) {
            if (part.contains("application/soap+xml")) {
                int start = part.indexOf("<?xml");
                if (start >= 0) soapEnvelope = part.substring(start).trim();
            } else if (part.contains("application/octet-stream")) {
                // Detect encoding
                String encoding = null;
                for (String headerLine : part.split("\r?\n")) {
                    if (headerLine.toLowerCase().startsWith("content-transfer-encoding:")) {
                        encoding = headerLine.split(":", 2)[1].trim().toLowerCase();
                        break;
                    }
                }
                int idx = part.indexOf("\r\n\r\n");
                if (idx < 0) idx = part.indexOf("\n\n");
                if (idx >= 0) {
                    int dataStart = idx + (part.charAt(idx) == '\r' ? 4 : 2);
                    String payloadSection = part.substring(dataStart).trim();
                    if (encoding != null && encoding.contains("base64")) {
                        // Clean and decode base64
                        payloadSection = payloadSection.replaceAll("(?m)^--.*$", "")
                                .replaceAll("[^A-Za-z0-9+/=]", "");
                        int mod = payloadSection.length() % 4;
                        if (mod > 0) {
                            payloadSection += "=".repeat(4 - mod);
                        }
                        try {
                            attachmentBytes = Base64.getDecoder().decode(payloadSection);
                            System.out.println("\uD83D\uDCE6 Base64 payload decoded successfully. Bytes: " + attachmentBytes.length);
                        } catch (IllegalArgumentException e) {
                            throw new RuntimeException("❌ Failed to decode Base64 attachment (after cleaning): " + e.getMessage(), e);
                        }
                    } else {
                        // BINARY: extract bytes from the original as4MessageBytes
                        int partOffset = as4MessageContent.indexOf(part);
                        if (partOffset >= 0) {
                            int payloadOffset = partOffset + dataStart;
                            int payloadEnd = payloadOffset + payloadSection.length();
                            attachmentBytes = Arrays.copyOfRange(as4MessageBytes, payloadOffset, payloadEnd);
                            System.out.println("\uD83D\uDCE6 Binary payload extracted. Bytes: " + attachmentBytes.length);
                        } else {
                            throw new RuntimeException("❌ Could not locate binary payload in original byte array");
                        }
                    }
                }
            }
        }

        if (soapEnvelope == null || attachmentBytes == null)
            throw new RuntimeException("❌ Missing SOAP envelope or attachment");

        // Step 3: Extract EncryptedKey from SOAP header
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document soapDoc = db.parse(new ByteArrayInputStream(soapEnvelope.getBytes(StandardCharsets.UTF_8)));

        NodeList encryptedKeyNodes = soapDoc.getElementsByTagNameNS("http://www.w3.org/2001/04/xmlenc#", "EncryptedKey");
        if (encryptedKeyNodes.getLength() == 0)
            throw new RuntimeException("❌ No EncryptedKey found");

        Element encryptedKeyElem = (Element) encryptedKeyNodes.item(0);
        String encryptedKeyB64 = encryptedKeyElem
                .getElementsByTagNameNS("http://www.w3.org/2001/04/xmlenc#", "CipherValue")
                .item(0).getTextContent().replaceAll("\\s+", "");

        // Step 4: Load keystore and decrypt symmetric key using configurable resource loader
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        Resource resource = resourceLoader.loadResource(keystoreBasePath, keystoreProperties.getPath());
        try (InputStream is = resource.getInputStream()) {
            keyStore.load(is, keystoreProperties.getPassword().toCharArray());
            LOGGER.info("✅ Keystore loaded from: {}",
                resourceLoader.getResourceLocation(keystoreBasePath + keystoreProperties.getPath()));
        }
        PrivateKey privateKey = (PrivateKey) keyStore.getKey("cert", keystoreProperties.getPassword().toCharArray());

        Cipher rsaCipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        OAEPParameterSpec oaepSpec = new OAEPParameterSpec("SHA-256", "MGF1",
                new MGF1ParameterSpec("SHA-256"), javax.crypto.spec.PSource.PSpecified.DEFAULT);
        rsaCipher.init(Cipher.DECRYPT_MODE, privateKey, oaepSpec);
        byte[] symmetricKeyBytes = rsaCipher.doFinal(Base64.getDecoder().decode(encryptedKeyB64));
        System.out.println("🔑 Symmetric key decrypted. Length: " + symmetricKeyBytes.length);

        // Step 5: Decrypt AES-GCM payload
        if (attachmentBytes.length < 28)
            throw new RuntimeException("❌ Attachment too short for AES-GCM");

        byte[] iv = Arrays.copyOfRange(attachmentBytes, 0, 12);
        byte[] encryptedPayload = Arrays.copyOfRange(attachmentBytes, 12, attachmentBytes.length);

        Cipher aesCipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
        SecretKeySpec aesKey = new SecretKeySpec(symmetricKeyBytes, "AES");

        byte[] decryptedBytes;
        try {
            aesCipher.init(Cipher.DECRYPT_MODE, aesKey, gcmSpec);
            decryptedBytes = aesCipher.doFinal(encryptedPayload);
            System.out.println("✅ AES-GCM decryption successful");
        } catch (Exception e) {
            throw new RuntimeException("❌ AES-GCM decryption failed: " + e.getMessage(), e);
        }

        // Step 6: Check compression
        String compressionType = null;
        NodeList props = soapDoc.getElementsByTagNameNS(
                "http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/", "Property");
        for (int i = 0; i < props.getLength(); i++) {
            Element prop = (Element) props.item(i);
            if ("CompressionType".equals(prop.getAttribute("name"))) {
                compressionType = prop.getTextContent().trim();
                break;
            }
        }

        byte[] ublXmlBytes;
        if ("application/gzip".equalsIgnoreCase(compressionType)) {
            try (GZIPInputStream gis = new GZIPInputStream(new ByteArrayInputStream(decryptedBytes))) {
                ublXmlBytes = gis.readAllBytes();
                System.out.println("🗜️ GZIP decompression successful");
            }
        } else {
            ublXmlBytes = decryptedBytes;
        }

        String ublXml = new String(ublXmlBytes, StandardCharsets.UTF_8);
        System.out.println("📄 Decrypted UBL XML:\n" + ublXml);
        return ublXml;
    }

}
