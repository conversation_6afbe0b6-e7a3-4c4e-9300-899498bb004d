package com.morohub.apsp.config.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * Configuration for a specific country
 */
public class CountryConfig {

    @JsonProperty("name")
    private String name;

    @JsonProperty("countryCode")
    private String countryCode;

    @JsonProperty("as4Config")
    private Map<String, Object> as4Config;

    @JsonProperty("documentTypes")
    private Map<String, DocumentTypeConfig> documentTypes;

    // Default constructor
    public CountryConfig() {}

    // Constructor with all fields
    public CountryConfig(String name, String countryCode, Map<String, Object> as4Config, Map<String, DocumentTypeConfig> documentTypes) {
        this.name = name;
        this.countryCode = countryCode;
        this.as4Config = as4Config;
        this.documentTypes = documentTypes;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Map<String, Object> getAs4Config() {
        return as4Config;
    }

    public void setAs4Config(Map<String, Object> as4Config) {
        this.as4Config = as4Config;
    }

    public Map<String, DocumentTypeConfig> getDocumentTypes() {
        return documentTypes;
    }

    public void setDocumentTypes(Map<String, DocumentTypeConfig> documentTypes) {
        this.documentTypes = documentTypes;
    }

    /**
     * Get document type configuration by type name
     */
    public DocumentTypeConfig getDocumentType(String documentType) {
        if (documentTypes == null) {
            return null;
        }
        return documentTypes.get(documentType.toUpperCase());
    }

    /**
     * Check if document type is supported
     */
    public boolean supportsDocumentType(String documentType) {
        return documentTypes != null && documentTypes.containsKey(documentType.toUpperCase());
    }

    // Helper methods for AS4 configuration
    @SuppressWarnings("unchecked")
    public String getDefaultSenderParticipantId() {
        if (as4Config != null && as4Config.containsKey("defaultSenderParticipantId")) {
            return (String) as4Config.get("defaultSenderParticipantId");
        }
        return "9915:test-sender";
    }

    @SuppressWarnings("unchecked")
    public String getDefaultReceiverParticipantId() {
        if (as4Config != null && as4Config.containsKey("defaultReceiverParticipantId")) {
            return (String) as4Config.get("defaultReceiverParticipantId");
        }
        return "9922:test-receiver";
    }

    @Override
    public String toString() {
        return "CountryConfig{" +
                "name='" + name + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", as4Config=" + as4Config +
                ", documentTypes=" + documentTypes +
                '}';
    }
}
