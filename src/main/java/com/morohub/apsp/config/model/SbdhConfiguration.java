package com.morohub.apsp.config.model;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * SBDH Configuration that combines global and document-specific settings
 */
public class SbdhConfiguration {
    private final JsonNode globalDefaults;
    private final DocumentTypeConfig documentConfig;

    public SbdhConfiguration(JsonNode globalDefaults, DocumentTypeConfig documentConfig) {
        this.globalDefaults = globalDefaults;
        this.documentConfig = documentConfig;
    }

    public String getHeaderVersion() {
        return getGlobalDefault("sbdhConfiguration.headerVersion", "1.0");
    }

    public String getAuthority() {
        return getGlobalDefault("sbdhConfiguration.authority", "iso6523-actorid-upis");
    }

    public String getStandard() {
        return documentConfig.getSbdhStandard();
    }

    public String getTypeVersion() {
        return documentConfig.getSbdhTypeVersion();
    }

    public String getDocumentType() {
        return documentConfig.getSbdhDocumentType();
    }

    public String getDocumentIdScopeType() {
        return getGlobalDefault("sbdhConfiguration.documentIdScope.type", "DOCUMENTID");
    }

    public String getDocumentIdScopeIdentifier() {
        return getGlobalDefault("sbdhConfiguration.documentIdScope.identifier", "busdox-docid-qns");
    }

    public String getProcessIdScopeType() {
        return getGlobalDefault("sbdhConfiguration.processIdScope.type", "PROCESSID");
    }

    public String getProcessIdScopeIdentifier() {
        return getGlobalDefault("sbdhConfiguration.processIdScope.identifier", "cenbii-procid-ubl");
    }

    private String getGlobalDefault(String path, String defaultValue) {
        JsonNode node = globalDefaults;
        for (String part : path.split("\\.")) {
            node = node.get(part);
            if (node == null) {
                return defaultValue;
            }
        }
        return node.asText(defaultValue);
    }
}
