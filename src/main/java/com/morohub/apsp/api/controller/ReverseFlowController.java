package com.morohub.apsp.api.controller;

import com.morohub.apsp.core.service.InvoiceService;
import com.morohub.apsp.core.service.RecipientService;
import com.morohub.apsp.config.CountryConfigurationService;
import com.morohub.apsp.core.service.Phase4AS4ReceiverService;
import com.morohub.apsp.common.dto.RecipientEndpointResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Controller for handling reverse flow - receiving AS4 messages and sending responses
 * All AS4 message processing is now handled by Phase4 built-in receiver
 */
@RestController
@RequestMapping("/reverse-flow")
public class ReverseFlowController {

    private static final Logger logger = LoggerFactory.getLogger(ReverseFlowController.class);

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private RecipientService recipientService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    @Autowired
    private Phase4AS4ReceiverService phase4ReceiverService;

  /*
     * AS4 message endpoint - redirects to Phase4 servlet
     * All AS4 messages should be sent directly to /reverse-flow/as4/* for proper Phase4 handling
     */
    @PostMapping(value = "/receive-as4-message")
    public ResponseEntity<String> receiveAS4Message() {
        logger.info("🔄 AS4 message endpoint called - redirecting to Phase4 servlet");

        return ResponseEntity.status(HttpStatus.PERMANENT_REDIRECT)
                .header("Location", "/reverse-flow/as4/")
                .contentType(MediaType.TEXT_PLAIN)
                .body("AS4 messages should be sent to /reverse-flow/as4/ for proper Phase4 handling");
    }






    /**
     * Get recipient endpoint information
     */
    @GetMapping("/recipient/{endpointId}")
    public ResponseEntity<RecipientEndpointResponseDTO> getRecipientEndpoint(@PathVariable String endpointId) {
        try {
            logger.info("Looking up recipient endpoint: {}", endpointId);
            
            RecipientEndpointResponseDTO endpoint = recipientService.getRecipientEndpoint(endpointId);
            
            logger.info("Found recipient endpoint: {}", endpoint.getEndpointUrl());
            return ResponseEntity.ok(endpoint);
            
        } catch (Exception e) {
            logger.error("Failed to get recipient endpoint: " + endpointId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(null);
        }
    }

    /**
     * Save or update recipient endpoint
     */
    @PostMapping("/recipient")
    public ResponseEntity<RecipientEndpointResponseDTO> saveRecipientEndpoint(
            @RequestParam("endpointId") String endpointId,
            @RequestParam("participantId") String participantId,
            @RequestParam("endpointUrl") String endpointUrl) {
        try {
            logger.info("Saving recipient endpoint: {} -> {}", endpointId, endpointUrl);
            
            RecipientEndpointResponseDTO saved = recipientService.saveRecipientEndpoint(
                endpointId, participantId, endpointUrl);
            
            logger.info("Successfully saved recipient endpoint");
            return ResponseEntity.ok(saved);
            
        } catch (Exception e) {
            logger.error("Failed to save recipient endpoint", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    /**
     * Get all active recipients
     */
    @GetMapping("/recipients")
    public ResponseEntity<?> getAllActiveRecipients() {
        try {
            logger.info("Retrieving all active recipients");
            
            var recipients = recipientService.getAllActiveRecipients();
            
            logger.info("Found {} active recipients", recipients.size());
            return ResponseEntity.ok(recipients);
            
        } catch (Exception e) {
            logger.error("Failed to get active recipients", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving recipients: " + e.getMessage());
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Reverse flow service is running");
    }



    /**
     * Get supported countries and document types
     */
    @GetMapping("/supported-configs")
    public ResponseEntity<Object> getSupportedConfigurations() {
        try {
            java.util.Map<String, Object> response = new java.util.HashMap<>();

            java.util.Set<String> countries = countryConfigurationService.getSupportedCountries();
            response.put("supportedCountries", countries);

            java.util.Map<String, java.util.Set<String>> countryDocTypes = new java.util.HashMap<>();
            for (String country : countries) {
                java.util.Set<String> docTypes = countryConfigurationService.getSupportedDocumentTypes(country);
                countryDocTypes.put(country, docTypes);
            }
            response.put("documentTypesByCountry", countryDocTypes);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get supported configurations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting supported configurations: " + e.getMessage());
        }
    }

    /**
     * Get configuration details for a specific country and document type
     */
    @GetMapping("/config/{countryCode}/{documentType}")
    public ResponseEntity<Object> getConfigurationDetails(
            @PathVariable String countryCode,
            @PathVariable String documentType) {
        try {
            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                return ResponseEntity.badRequest()
                        .body("Unsupported country/document type: " + countryCode + "/" + documentType);
            }

            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("countryCode", countryCode);
            response.put("documentType", documentType);
            response.put("classPath", countryConfigurationService.getUBLClass(countryCode, documentType).getName());
            response.put("factoryPath", countryConfigurationService.getFactoryClass(countryCode, documentType).getName());
            response.put("schematronFile", countryConfigurationService.getSchematronFile(countryCode, documentType));
            response.put("customizationId", countryConfigurationService.getCustomizationId(countryCode, documentType));
            response.put("profileId", countryConfigurationService.getProfileId(countryCode, documentType));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get configuration details for {}/{}", countryCode, documentType, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting configuration details: " + e.getMessage());
        }
    }

    /**
     * Extract country code from UBL XML
     */
    private String extractCountryCodeFromXml(String ublXml) {
        try {
            logger.debug("Extracting country code from UBL XML");

            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            // Look for country code in postal address elements
            org.w3c.dom.NodeList countryElements = doc.getElementsByTagName("IdentificationCode");
            for (int i = 0; i < countryElements.getLength(); i++) {
                org.w3c.dom.Element countryElement = (org.w3c.dom.Element) countryElements.item(i);
                // Check if this is within a Country element
                if (countryElement.getParentNode() != null &&
                    countryElement.getParentNode().getNodeName().contains("Country")) {
                    String countryCode = countryElement.getTextContent();
                    if (countryCode != null && !countryCode.trim().isEmpty()) {
                        logger.info("🌍 Found country code in address: {}", countryCode);
                        return countryCode.trim();
                    }
                }
            }

            logger.warn("⚠️ No country code found in UBL XML, using DEFAULT");
            return "DEFAULT";

        } catch (Exception e) {
            logger.error("❌ Error extracting country code from UBL XML: {}", e.getMessage());
            return "DEFAULT";
        }
    }

    /**
     * Extract document type from UBL XML root element
     */
    private String extractDocumentTypeFromXml(String ublXml) {
        try {
            logger.debug("Extracting document type from UBL XML");

            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            String rootElementName = doc.getDocumentElement().getLocalName();
            if (rootElementName == null) {
                rootElementName = doc.getDocumentElement().getNodeName();
            }

            // Map root element names to document types
            String documentType;
            switch (rootElementName.toLowerCase()) {
                case "invoice":
                    documentType = "INVOICE";
                    break;
                case "creditnote":
                    documentType = "CREDITNOTE";
                    break;
                case "applicationresponse":
                    documentType = "APPLICATIONRESPONSE";
                    break;
                default:
                    logger.warn("⚠️ Unknown document type: {}, defaulting to INVOICE", rootElementName);
                    documentType = "INVOICE";
                    break;
            }

            logger.info("📄 Detected document type: {} from root element: {}", documentType, rootElementName);
            return documentType;

        } catch (Exception e) {
            logger.error("❌ Error extracting document type from UBL XML: {}", e.getMessage());
            return "INVOICE"; // Default fallback
        }
    }
}
