package com.morohub.apsp.api.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.morohub.apsp.common.dto.PeppolReq;
import com.morohub.apsp.core.service.InvoiceService;
import com.morohub.apsp.config.CountryConfigurationService;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.Set;

@RestController
@RequestMapping("/invoices")
public class PeppolSbdInvoiceController {

	private static final Logger logger = LoggerFactory.getLogger(PeppolSbdInvoiceController.class);

    private final InvoiceService invoiceService;

    @Autowired
    Validator validator;
    
    @Value("${decrypt.api.host}")
    private String decryptHost;

  

    @Value("${decrypt.api.port}")
    private String decryptPort;
    
    @Value("${logging.api.port}")
    private String logPort;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Environment env;

    @Autowired
    private ObjectMapper objectMapper; // For JSON parsing

    @Autowired
    private CountryConfigurationService countryConfigurationService;




    public PeppolSbdInvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }



    @PostMapping(value = "/generateInvoice", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> sendPeppolSbdInvoice(
            @RequestBody PeppolReq req) {

        String status = "SUCCESS";
        String failureReason = "N/A";
        Object request = null;

        try {
            // Step 1: Validate country and document type
            String countryCode = req.getCountryCode() != null ? req.getCountryCode() : "DEFAULT";
            String documentType = req.getDocumentType() != null ? req.getDocumentType() : "INVOICE";

            logger.info("Processing request for country: {} and document type: {}", countryCode, documentType);

            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                return ResponseEntity.badRequest()
                    .body("Unsupported country/document type combination: " + countryCode + "/" + documentType);
            }

            // Step 2: Get dynamic class configuration
            Class<?> invoiceClass = countryConfigurationService.getUBLClass(countryCode, documentType);
            logger.info("Using UBL class: {} for {}/{}", invoiceClass.getName(), countryCode, documentType);

            // Step 3: Decrypt message
            String url = String.format("http://%s:%s/api/secure/decrypt-invoice/%s", decryptHost, decryptPort, req.getPeppolId());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(req.getReqMessage(), headers);

            ResponseEntity<JsonNode> response=restTemplate.postForEntity(url, entity, JsonNode.class);

            // Step 4: Convert JSON to DTO using dynamic class
            request = objectMapper.treeToValue(response.getBody(), invoiceClass);

            // Step 5: Validate
            Set<ConstraintViolation<Object>> violations = validator.validate(request);
            if (!violations.isEmpty()) {
                StringBuilder errorMessages = new StringBuilder();
                for (ConstraintViolation<Object> violation : violations) {
                    errorMessages.append(violation.getPropertyPath()).append(": ")
                            .append(violation.getMessage()).append("; ");
                }
                status = "FAILED";
                failureReason = errorMessages.toString();
                return ResponseEntity.badRequest().body("Validation failed: " + failureReason);
            }

            // Step 6: Generate invoice using country-specific configuration
            String responseXml = invoiceService.generateInvoiceWithConfig(request, countryCode, documentType);
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_XML)
                    .body(responseXml);

        } catch (Exception e) {
            status = "FAILED";
            failureReason = e.getMessage();
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error: " + failureReason);
        } finally {
            try {
                // Step 5: Prepare log data
                ObjectNode logPayload = objectMapper.createObjectNode();
                logPayload.put("peppolId",  req.getPeppolId());
               // logPayload.put("invoiceNumber", (JsonNode) (request != null ? request.getID() : "UNKNOWN"));
               // logPayload.put("invoiceDate", (request != null ? request.getIssueDate() : null).toString());
                logPayload.put("processDate", LocalDate.now().toString());
                logPayload.put("status", status);
                logPayload.put("failureReason", failureReason);

                // Step 6: Send log
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> entity = new HttpEntity<>(logPayload.toString(), headers);

                String logUrl = String.format("http://%s:%s/api/invoices/create", decryptHost, logPort);
                restTemplate.postForEntity(logUrl, entity, Void.class);

            } catch (Exception logEx) {
                logger.error("Failed to log invoice generation result", logEx);
            }
        }
    }


}

