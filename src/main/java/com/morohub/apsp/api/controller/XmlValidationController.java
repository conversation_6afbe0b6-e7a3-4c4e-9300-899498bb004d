package com.morohub.apsp.api.controller;

import java.util.List;
import java.util.stream.Collectors;

import com.morohub.apsp.core.service.AS4ConversionResult;
import com.morohub.apsp.core.service.AS4ConversionService;
import com.morohub.apsp.core.service.SchematronValidationService;
import com.morohub.apsp.config.CountryConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for UBL Invoice validation and AS4 conversion
 */
@RestController
@RequestMapping("/api/invoice")
public class XmlValidationController {

    @Autowired
    private SchematronValidationService schematronValidationService;

    @Autowired
    private AS4ConversionService as4ConversionService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    /**
     * Validate XML against Schematron rules and convert to AS4
     *
     * @param xml UBL invoice XML payload
     * @return AS4 conversion result
     */
    @PostMapping(value = "/validate-and-convert-to-as4",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateAndConvertToAs4(@RequestBody String xml) {
        try {
            // Step 1: Perform country-specific Schematron validation
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles("DEFAULT", "INVOICE");
            List<String> validationErrors = schematronValidationService.validateXmlWithMultipleSchematrons(xml, schematronFiles);

            if (!validationErrors.isEmpty()) {
                String errors = validationErrors.stream().collect(Collectors.joining("\n"));
                return ResponseEntity.badRequest()
                        .body(new ValidationErrorResponse("Schematron validation failed", validationErrors));
            }

            // Step 2: Convert to AS4 message
            AS4ConversionResult conversionResult = as4ConversionService.convertAndSend(xml);

            if (conversionResult.isSuccess()) {
                return ResponseEntity.ok(new AS4ConversionResponse(conversionResult));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ErrorResponse("AS4 conversion failed", conversionResult.getErrorMessage()));
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Processing failed", e.getMessage()));
        }
    }

    /**
     * Validate XML against Schematron rules only
     */
    @PostMapping(value = "/validate",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateXml(@RequestBody String xml) {
        try {
            List<String> validationErrors = schematronValidationService.validateXml(xml, "PEPPOL-EN16931-UBL.sch");

            if (validationErrors.isEmpty()) {
                return ResponseEntity.ok(new ValidationResponse("Validation successful", true));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ValidationErrorResponse("Validation failed", validationErrors));
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Validation failed", e.getMessage()));
        }
    }

    /**
     * Validate XML against country-specific Schematron rules
     */
    @PostMapping(value = "/validate/{countryCode}/{documentType}",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateXmlWithCountryConfig(
            @PathVariable String countryCode,
            @PathVariable String documentType,
            @RequestBody String xml) {
        try {
            // Validate country and document type support
            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("Unsupported configuration",
                            "Country/document type combination not supported: " + countryCode + "/" + documentType));
            }

            // Get country-specific schematron files
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles(countryCode, documentType);

            // Perform validation with multiple schematron files
            List<String> validationErrors = schematronValidationService.validateXmlWithMultipleSchematrons(xml, schematronFiles);

            if (validationErrors.isEmpty()) {
                return ResponseEntity.ok(new CountryValidationResponse(
                    "Country-specific validation successful", true, countryCode, documentType, schematronFiles));
            } else {
                return ResponseEntity.badRequest()
                        .body(new CountryValidationErrorResponse(
                            "Country-specific validation failed", validationErrors, countryCode, documentType, schematronFiles));
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Country-specific validation failed", e.getMessage()));
        }
    }

    /**
     * Validate and convert with country-specific configuration
     */
    @PostMapping(value = "/validate-and-convert-to-as4/{countryCode}/{documentType}",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateAndConvertToAs4WithCountryConfig(
            @PathVariable String countryCode,
            @PathVariable String documentType,
            @RequestBody String xml) {
        try {
            // Validate country and document type support
            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("Unsupported configuration",
                            "Country/document type combination not supported: " + countryCode + "/" + documentType));
            }

            // Step 1: Perform country-specific Schematron validation
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles(countryCode, documentType);
            List<String> validationErrors = schematronValidationService.validateXmlWithMultipleSchematrons(xml, schematronFiles);

            if (!validationErrors.isEmpty()) {
                String errors = validationErrors.stream().collect(Collectors.joining("\n"));
                return ResponseEntity.badRequest()
                        .body(new CountryValidationErrorResponse(
                            "Country-specific schematron validation failed", validationErrors, countryCode, documentType, schematronFiles));
            }

            // Step 2: Convert to AS4 message
            AS4ConversionResult conversionResult = as4ConversionService.convertAndSend(xml);

            if (conversionResult.isSuccess()) {
                return ResponseEntity.ok(new CountryAS4ConversionResponse(conversionResult, countryCode, documentType, schematronFiles));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ErrorResponse("AS4 conversion failed", conversionResult.getErrorMessage()));
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Processing failed", e.getMessage()));
        }
    }

    /**
     * Get AS4 system status
     */
    @GetMapping(value = "/as4/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> getAS4Status() {
        try {
            String status = as4ConversionService.getSystemStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting AS4 status: " + e.getMessage());
        }
    }

    // Response DTOs
    public static class ValidationResponse {
        private String message;
        private boolean valid;

        public ValidationResponse(String message, boolean valid) {
            this.message = message;
            this.valid = valid;
        }

        // Getters
        public String getMessage() { return message; }
        public boolean isValid() { return valid; }
    }

    public static class ValidationErrorResponse {
        private String message;
        private List<String> errors;

        public ValidationErrorResponse(String message, List<String> errors) {
            this.message = message;
            this.errors = errors;
        }

        // Getters
        public String getMessage() { return message; }
        public List<String> getErrors() { return errors; }
    }

    public static class AS4ConversionResponse {
        private boolean success;
        private String message;
        private String as4MessageId;
        private String endpointUsed;
        private long duration;

        public AS4ConversionResponse(AS4ConversionResult result) {
            this.success = result.isSuccess();
            this.message = result.getMessage();
            this.as4MessageId = result.getAs4MessageId();
            this.endpointUsed = result.getEndpointUsed();
            this.duration = result.getDuration();
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getAs4MessageId() { return as4MessageId; }
        public String getEndpointUsed() { return endpointUsed; }
        public long getDuration() { return duration; }
    }

    public static class ErrorResponse {
        private String error;
        private String details;

        public ErrorResponse(String error, String details) {
            this.error = error;
            this.details = details;
        }

        // Getters
        public String getError() { return error; }
        public String getDetails() { return details; }
    }

    public static class CountryValidationResponse {
        private String message;
        private boolean valid;
        private String countryCode;
        private String documentType;
        private String schematronFile;
        private List<String> schematronFiles;

        // Constructor for backward compatibility
        public CountryValidationResponse(String message, boolean valid, String countryCode, String documentType, String schematronFile) {
            this.message = message;
            this.valid = valid;
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFile = schematronFile;
        }

        // Constructor for multiple schematron files
        public CountryValidationResponse(String message, boolean valid, String countryCode, String documentType, List<String> schematronFiles) {
            this.message = message;
            this.valid = valid;
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFiles = schematronFiles;
            // Set first file for backward compatibility
            this.schematronFile = (schematronFiles != null && !schematronFiles.isEmpty()) ? schematronFiles.get(0) : null;
        }

        // Getters
        public String getMessage() { return message; }
        public boolean isValid() { return valid; }
        public String getCountryCode() { return countryCode; }
        public String getDocumentType() { return documentType; }
        public String getSchematronFile() { return schematronFile; }
        public List<String> getSchematronFiles() { return schematronFiles; }
    }

    public static class CountryValidationErrorResponse {
        private String message;
        private List<String> errors;
        private String countryCode;
        private String documentType;
        private String schematronFile;
        private List<String> schematronFiles;

        // Constructor for backward compatibility
        public CountryValidationErrorResponse(String message, List<String> errors, String countryCode, String documentType, String schematronFile) {
            this.message = message;
            this.errors = errors;
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFile = schematronFile;
        }

        // Constructor for multiple schematron files
        public CountryValidationErrorResponse(String message, List<String> errors, String countryCode, String documentType, List<String> schematronFiles) {
            this.message = message;
            this.errors = errors;
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFiles = schematronFiles;
            // Set first file for backward compatibility
            this.schematronFile = (schematronFiles != null && !schematronFiles.isEmpty()) ? schematronFiles.get(0) : null;
        }

        // Getters
        public String getMessage() { return message; }
        public List<String> getErrors() { return errors; }
        public String getCountryCode() { return countryCode; }
        public String getDocumentType() { return documentType; }
        public String getSchematronFile() { return schematronFile; }
        public List<String> getSchematronFiles() { return schematronFiles; }
    }

    public static class CountryAS4ConversionResponse {
        private boolean success;
        private String message;
        private String as4MessageId;
        private String endpointUsed;
        private long duration;
        private String countryCode;
        private String documentType;
        private String schematronFile;
        private List<String> schematronFiles;

        // Constructor for backward compatibility
        public CountryAS4ConversionResponse(AS4ConversionResult result, String countryCode, String documentType, String schematronFile) {
            this.success = result.isSuccess();
            this.message = result.getMessage();
            this.as4MessageId = result.getAs4MessageId();
            this.endpointUsed = result.getEndpointUsed();
            this.duration = result.getDuration();
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFile = schematronFile;
        }

        // Constructor for multiple schematron files
        public CountryAS4ConversionResponse(AS4ConversionResult result, String countryCode, String documentType, List<String> schematronFiles) {
            this.success = result.isSuccess();
            this.message = result.getMessage();
            this.as4MessageId = result.getAs4MessageId();
            this.endpointUsed = result.getEndpointUsed();
            this.duration = result.getDuration();
            this.countryCode = countryCode;
            this.documentType = documentType;
            this.schematronFiles = schematronFiles;
            // Set first file for backward compatibility
            this.schematronFile = (schematronFiles != null && !schematronFiles.isEmpty()) ? schematronFiles.get(0) : null;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getAs4MessageId() { return as4MessageId; }
        public String getEndpointUsed() { return endpointUsed; }
        public long getDuration() { return duration; }
        public String getCountryCode() { return countryCode; }
        public String getDocumentType() { return documentType; }
        public String getSchematronFile() { return schematronFile; }
        public List<String> getSchematronFiles() { return schematronFiles; }
    }
}
