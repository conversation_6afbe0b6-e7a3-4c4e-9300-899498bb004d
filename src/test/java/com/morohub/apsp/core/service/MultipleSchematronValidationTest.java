package com.morohub.apsp.core.service;

import com.morohub.apsp.config.model.DocumentTypeConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for multiple schematron validation functionality
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
public class MultipleSchematronValidationTest {

    private DocumentTypeConfig documentTypeConfig;
    private SchematronValidationService schematronValidationService;
    private CountryConfigurationService countryConfigurationService;

    @BeforeEach
    void setUp() {
        documentTypeConfig = new DocumentTypeConfig();
        schematronValidationService = new SchematronValidationService();
    }

    @Test
    void testDocumentTypeConfigWithMultipleSchematronFiles() {
        // Test setting multiple schematron files
        List<String> schematronFiles = Arrays.asList(
            "PINT-UBL-validation-preprocessed.sch",
            "PINT-jurisdiction-aligned-rules.sch",
            "PEPPOL-EN16931-UBL.sch"
        );
        
        documentTypeConfig.setSchematronFiles(schematronFiles);
        
        // Verify the files are set correctly
        List<String> retrievedFiles = documentTypeConfig.getSchematronFiles();
        assertEquals(3, retrievedFiles.size());
        assertEquals("PINT-UBL-validation-preprocessed.sch", retrievedFiles.get(0));
        assertEquals("PINT-jurisdiction-aligned-rules.sch", retrievedFiles.get(1));
        assertEquals("PEPPOL-EN16931-UBL.sch", retrievedFiles.get(2));
    }

    @Test
    void testBackwardCompatibilityWithSingleSchematronFile() {
        // Test backward compatibility with single schematron file
        String singleFile = "PEPPOL-EN16931-UBL.sch";
        documentTypeConfig.setSchematronFile(singleFile);
        
        // When schematronFiles is null but schematronFile is set, 
        // getSchematronFiles() should return a list with the single file
        List<String> retrievedFiles = documentTypeConfig.getSchematronFiles();
        assertEquals(1, retrievedFiles.size());
        assertEquals(singleFile, retrievedFiles.get(0));
        
        // Also verify the single file getter still works
        assertEquals(singleFile, documentTypeConfig.getSchematronFile());
    }

    @Test
    void testMultipleSchematronFilesOverrideSingleFile() {
        // Test that when both are set, schematronFiles takes precedence
        String singleFile = "PEPPOL-EN16931-UBL.sch";
        List<String> multipleFiles = Arrays.asList(
            "PINT-UBL-validation-preprocessed.sch",
            "PINT-jurisdiction-aligned-rules.sch"
        );
        
        documentTypeConfig.setSchematronFile(singleFile);
        documentTypeConfig.setSchematronFiles(multipleFiles);
        
        // getSchematronFiles() should return the multiple files
        List<String> retrievedFiles = documentTypeConfig.getSchematronFiles();
        assertEquals(2, retrievedFiles.size());
        assertEquals("PINT-UBL-validation-preprocessed.sch", retrievedFiles.get(0));
        assertEquals("PINT-jurisdiction-aligned-rules.sch", retrievedFiles.get(1));
        
        // Single file getter should still return the single file
        assertEquals(singleFile, documentTypeConfig.getSchematronFile());
    }

    @Test
    void testEmptySchematronFilesList() {
        // Test with empty list
        documentTypeConfig.setSchematronFiles(Arrays.asList());
        
        List<String> retrievedFiles = documentTypeConfig.getSchematronFiles();
        assertNotNull(retrievedFiles);
        assertTrue(retrievedFiles.isEmpty());
    }

    @Test
    void testNullSchematronFiles() {
        // Test with null schematronFiles and null schematronFile
        documentTypeConfig.setSchematronFiles(null);
        documentTypeConfig.setSchematronFile(null);
        
        List<String> retrievedFiles = documentTypeConfig.getSchematronFiles();
        assertNotNull(retrievedFiles);
        assertTrue(retrievedFiles.isEmpty());
    }

    @Test
    void testToStringIncludesSchematronFiles() {
        // Test that toString includes both schematronFile and schematronFiles
        List<String> schematronFiles = Arrays.asList(
            "PINT-UBL-validation-preprocessed.sch",
            "PINT-jurisdiction-aligned-rules.sch"
        );
        
        documentTypeConfig.setSchematronFile("PEPPOL-EN16931-UBL.sch");
        documentTypeConfig.setSchematronFiles(schematronFiles);
        documentTypeConfig.setClassPath("test.class.path");
        documentTypeConfig.setFactoryPath("test.factory.path");
        
        String toString = documentTypeConfig.toString();
        assertTrue(toString.contains("schematronFile"));
        assertTrue(toString.contains("schematronFiles"));
        assertTrue(toString.contains("PINT-UBL-validation-preprocessed.sch"));
        assertTrue(toString.contains("PINT-jurisdiction-aligned-rules.sch"));
    }
}
